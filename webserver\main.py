"""
FastAPI 金融市场数据服务主应用
"""

import asyncio
from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from loguru import logger

from app.api.v1 import market
from app.core.background import BackgroundTaskManager
from app.core.exceptions import setup_exception_handlers
from app.core.logging import setup_logging
from config.settings import get_settings


# 全局后台任务管理器
background_manager = BackgroundTaskManager()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("启动金融市场数据服务...")
    
    # 启动后台任务
    await background_manager.start()
    logger.info("后台任务已启动")
    
    yield
    
    # 关闭时执行
    logger.info("正在关闭金融市场数据服务...")
    await background_manager.stop()
    logger.info("后台任务已停止")


def create_app() -> FastAPI:
    """创建FastAPI应用实例"""
    settings = get_settings()
    
    # 设置日志
    setup_logging()
    
    # 创建FastAPI应用
    app = FastAPI(
        title="金融市场数据服务",
        description="提供股票成交信息、K线数据等金融市场数据的API服务",
        version="1.0.0",
        docs_url="/docs",
        redoc_url="/redoc",
        lifespan=lifespan
    )
    
    # 设置CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.ALLOWED_ORIGINS,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 设置异常处理
    setup_exception_handlers(app)
    
    # 注册路由
    app.include_router(
        market.router,
        prefix="/api/v1/market",
        tags=["市场数据"]
    )
    
    return app


# 创建应用实例
app = create_app()


@app.get("/")
async def root():
    """根路径健康检查"""
    return {
        "code": 200,
        "message": "金融市场数据服务运行正常",
        "data": {
            "service": "market-data-api",
            "version": "1.0.0",
            "status": "running"
        }
    }


@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {
        "code": 200,
        "message": "服务健康",
        "data": {
            "status": "healthy",
            "background_tasks": background_manager.is_running()
        }
    }


if __name__ == "__main__":
    import uvicorn
    settings = get_settings()
    
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info"
    )
