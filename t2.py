import csv
import taosws
import datetime
conf = {
    "td.connect.websocket.scheme": "ws",
    "td.connect.ip": "**************",           # 服务器地址
    "td.connect.port": "6041",              # WebSocket端口
    "td.connect.user": "root",              # 用户名
    "td.connect.pass": "taosdata",          # 密码
    "group.id": "tock_0",
    "auto.offset.reset": "earliest",
    # "auto.offset.reset": "latest",
}
consumer = taosws.Consumer(conf)
consumer.subscribe(["tick"])
header = ["vt_symbol", "datetime", "last_price", "high", "low", 
          "pre_close", "volume", "amount", "local_time"]
all_data = []
try:
    while True:
        # 拉取消息（超时1秒）
        message = consumer.poll(timeout=2.0)
        if not message:
            continue
        data_lst = []
        for block in message:
            data_lst.extend(
                 block.fetchall()
            )
        consumer.commit(message)
        now = datetime.datetime.now()
        if now.minute not in (25, 50):
            continue
        for x in data_lst:
            x  = list(x)
            x.append(now)
            all_data.append(x)
        if now.hour == 15 and now.minute >= 30:
            break
except Exception as e:
    print(f"消费过程中出错: {e}")
finally:
    # 清理资源
    consumer.unsubscribe()
    consumer.close()
filename = "0001.csv"
with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
    writer = csv.writer(csvfile)
        # 先写入表头
    writer.writerow(header)
    
    # 再写入所有数据行
    writer.writerows(all_data)