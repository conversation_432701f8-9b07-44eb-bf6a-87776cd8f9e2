import time
from datetime import datetime
from collections.abc import Callable
import pandas as pd
import taosws
from utils.constant import Exchange, Interval
from utils.object import BarData, TickData
from utils.database import (
    BaseDatabase,
    BarOverview,
    TickOverview,
)
from utils.setting import SETTINGS
from .taosws_base import TaoswsBase
from .taosws_script import (
    # CREATE_DATABASE_SCRIPT,
    BAR_TABLE,
    TICK_TABLE,
)


class TaoswsDatabase(BaseDatabase):
    """TDengine数据库接口"""

    def __init__(self, config: dict=None) -> None:
        """构造函数"""
        env = {}
        env.update(SETTINGS)
        if config:
            env.update(config)
        self.db_name: str = env["database.database"]
        self.conn = None
        self.tick_stmt2 = None
        self.bar_stmt2_dct = {}
        self.taosws_client = TaoswsBase(env)
        self.get_conn = self.taosws_client.get_conn
        self.connection = self.taosws_client.connection
        self.cursor_execute = self.taosws_client.cursor_execute
        self.fetchall = self.taosws_client.fetchall


    def init_db(self):
        self.conn = self.taosws_client.get_conn()
        # with self.taosws_client.connection() as conn:
        self.conn.execute(f"CREATE DATABASE IF NOT EXISTS {self.db_name} KEEP 36500")
        self.conn.execute(f"use {self.db_name}")
        cursor = self.conn.cursor()
        cursor.execute(f"use {self.db_name}")
        # bar 超级表
        col_defs = [f"{name} {type_}" for name, type_ in BAR_TABLE["keys"].items()]
        tag_defs = [f"{name} {type_}" for name, type_ in BAR_TABLE["tags"].items()]
        # 日线超级表
        table_name = Interval.DAILY.value
        sql = f"CREATE STABLE IF NOT EXISTS {table_name} ({','.join(col_defs)}) TAGS ({','.join(tag_defs)})"
        cursor.execute(sql)
        # 1分钟超级表
        table_name = Interval.MINUTE.value
        sql = f"CREATE STABLE IF NOT EXISTS {table_name} ({','.join(col_defs)}) TAGS ({','.join(tag_defs)})"
        cursor.execute(sql)

        # tick 超级表
        col_defs = [f"{name} {type_}" for name, type_ in TICK_TABLE["keys"].items()]
        tag_defs = [f"{name} {type_}" for name, type_ in TICK_TABLE["tags"].items()]
        table_name = Interval.TICK.value
        sql = f"CREATE STABLE IF NOT EXISTS {table_name} ({','.join(col_defs)}) TAGS ({','.join(tag_defs)})"
        cursor.execute(sql)
        cursor.close()

    def get_tick_stmt2(self, interval: Interval=Interval.TICK):
        if self.tick_stmt2:
            return self.tick_stmt2
        interval_str = interval.value
        sql = f"insert into ? using {interval_str} tags ({','.join(['?' for _ in TICK_TABLE['tags']])}) values ({','.join(['?' for _ in TICK_TABLE['keys']])})"
        self.tick_stmt2 = self.conn.stmt2_statement()
        self.tick_stmt2.prepare(sql)
        return self.tick_stmt2

    def get_bar_stmt2(self, interval: Interval):
        interval_str = interval.value
        bar_stmt2 = self.bar_stmt2_dct.get(interval_str)
        if bar_stmt2:
            return bar_stmt2
        sql = f"insert into ? using {interval_str} tags ({','.join(['?' for _ in BAR_TABLE['tags']])}) values ({','.join(['?' for _ in BAR_TABLE['keys']])})"
        bar_stmt2 = self.conn.stmt2_statement()
        bar_stmt2.prepare(sql)
        self.bar_stmt2_dct[interval_str] = bar_stmt2
        return bar_stmt2
    
    def save_tick_data(self, ticks: list[TickData]) -> int:
        """保存tick数据"""
        stmt2 = self.get_tick_stmt2()
        stmt2param_lst = []
        for tick in ticks:
            stmt2param = self.get_stmt2param_tick([tick])
            if not stmt2param:
                continue
            stmt2param_lst.append(stmt2param)

        stmt2.bind(stmt2param_lst)
        count = stmt2.execute()
        
        return count
    
    def save_bar_data(self, bars: list[BarData], interval: Interval) -> int:
        """保存k线数据"""
        stmt2 = self.get_bar_stmt2(interval=interval)
        stmt2param_lst = []
        for bar in bars:
            stmt2param = self.get_stmt2param_bar([bar], interval=interval)
            if not stmt2param:
                continue
            stmt2param_lst.append(stmt2param)
        if not stmt2param_lst:
            return
        stmt2.bind(stmt2param_lst)
        count = stmt2.execute()
        
        return count
    
    def get_stmt2param_tick(self, ticks:list[TickData], interval: Interval = Interval.TICK):
        "同一个子表的数据"
        table_name = None
        tags = None
        col_dct = {}
        for tick in ticks:
            if not table_name:
                symbol: str = tick.symbol
                exchange: str = tick.exchange
                table_name=f"{interval.value}_{symbol}_{exchange.lower()}"
                tags=[
                    taosws.varchar_to_tag(tick.vt_symbol),
                     taosws.varchar_to_tag(symbol),
                     taosws.varchar_to_tag(exchange),
                ]
            for k in TICK_TABLE["keys"]:
                if k == "datetime":
                    v = tick.timestamp
                elif k == "direction":
                    v = tick.direction.value
                else:
                    v = getattr(tick, k)
                col_dct.setdefault(k, []).append(v)
        columns = []
        for k, col in col_dct.items():
            if k == "datetime":
                columns.append(
                    taosws.millis_timestamps_to_column(col)
                )
            else:
                columns.append(
                    taosws.doubles_to_column(col)
                )
        stmt2param = taosws.stmt2_bind_param_view(
            table_name=table_name, 
            tags=tags,
            columns=columns
        )
        return stmt2param


    def get_stmt2param_bar(self, bars:list[BarData], interval: Interval):
        "同一个子表的数据"
        table_name = None
        tags = None
        col_dct = {}
        for bar in bars:
            if not table_name:
                symbol: str = bar.symbol
                exchange: str = bar.exchange
                table_name=f"{interval.value}_{symbol}_{exchange.lower()}"
                tags=[
                    taosws.varchar_to_tag(bar.vt_symbol),
                    taosws.varchar_to_tag(symbol),
                    taosws.varchar_to_tag(exchange),
                ]
            for k in BAR_TABLE["keys"]:
                if k == "datetime":
                    v = int(bar.datetime.timestamp() * 1000)
                else:
                    v = getattr(bar, k)
                col_dct.setdefault(k, []).append(v)
        columns = []
        for k, col in col_dct.items():
            if k == "datetime":
                columns.append(
                    taosws.millis_timestamps_to_column(col)
                )
            else:
                columns.append(
                    taosws.doubles_to_column(col)
                )
        stmt2param = taosws.stmt2_bind_param_view(
            table_name=table_name, 
            tags=tags,
            columns=columns
        )
        return stmt2param
      

    def load_bar_data(
        self,
        symbol: str,
        exchange: Exchange,
        interval: Interval,
        start: datetime,
        end: datetime
    ) -> list[BarData]:
        """读取K线数据"""
        # 生成数据表名
        table_name: str = "_".join(["bar", symbol.replace("-", "_"), exchange.value, interval.value])

        # 从数据库读取数据
        df: pd.DataFrame = pd.read_sql(f"select *, interval_ from {table_name} WHERE datetime BETWEEN '{start}' AND '{end}'", self.conn)

        # 返回BarData列表
        bars: list[BarData] = []

        for row in df.itertuples():
            bar: BarData = BarData(
                symbol=symbol,
                exchange=exchange,
                datetime=row.datetime,
                interval=Interval(row.interval_),
                volume=row.volume,
                turnover=row.turnover,
                open_interest=row.open_interest,
                open_price=row.open_price,
                high_price=row.high_price,
                low_price=row.low_price,
                close_price=row.close_price,
                gateway_name="DB"
            )
            bars.append(bar)

        return bars

    def load_tick_data(
        self,
        vt_symbol: str,
        start_time: str=None,
        end_time: str=None,
    ) -> list[TickData]:
        """读取tick数据"""
        # 生成数据表名
        symbol, exchange = vt_symbol.split(".")
        where = ""
        if start_time and end_time:
            where += "WHERE datetime >= '{start_time}' AND datetime <= '{end_time}'"
        else:
            if start_time:
                where += f"WHERE datetime >= '{start_time}'"
            if end_time:
                where += f"WHERE datetime <= '{end_time}'"
        table_name: str = "_".join([Interval.TICK.value, symbol, exchange.lower()])
        sql = f"select * from {self.db_name}.{table_name} {where};"
        print(sql)
        d_lst = self.taosws_client.fetchall(sql, is_dict=True)
        # 返回TickData列表
        ticks: list[TickData] = []

        for d in d_lst:
            datetime=d["datetime"]
            tick: TickData = TickData(
                symbol=symbol,
                exchange=exchange,
                datetime=datetime,
                timestamp=int(datetime.timestamp() * 1000),
                last_price=d["last_price"],
                open=d["open"],
                high=d["high"],
                low=d["low"],
                pre_close=d["pre_close"],
                volume=d["volume"],
                amount=d["amount"],
                bid_price_1=d["bid_price_1"],
                bid_price_2=d["bid_price_2"],
                bid_price_3=d["bid_price_3"],
                bid_price_4=d["bid_price_4"],
                bid_price_5=d["bid_price_5"],
                ask_price_1=d["ask_price_1"],
                ask_price_2=d["ask_price_2"],
                ask_price_3=d["ask_price_3"],
                ask_price_4=d["ask_price_4"],
                ask_price_5=d["ask_price_5"],
                bid_volume_1=d["bid_volume_1"],
                bid_volume_2=d["bid_volume_2"],
                bid_volume_3=d["bid_volume_3"],
                bid_volume_4=d["bid_volume_4"],
                bid_volume_5=d["bid_volume_5"],
                ask_volume_1=d["ask_volume_1"],
                ask_volume_2=d["ask_volume_2"],
                ask_volume_3=d["ask_volume_3"],
                ask_volume_4=d["ask_volume_4"],
                ask_volume_5=d["ask_volume_5"],
            )
            ticks.append(tick)
        return ticks

    def delete_bar_data(
        self,
        symbol: str,
        exchange: Exchange,
        interval: Interval
    ) -> int:
        """删除K线数据"""
        # 生成数据表名
        table_name: str = "_".join(["bar", symbol.replace("-", "_"), exchange.value, interval.value])

        # 查询数据条数
        self.cursor.execute(f"select count(*) from {table_name}")
        result: list = self.cursor.fetchall()
        count: int = int(result[0][0])

        # 执行K线删除
        self.cursor.execute(f"DROP TABLE {table_name}")

        return count

    def delete_tick_data(
        self,
        symbol: str,
        exchange: Exchange
    ) -> int:
        """删除tick数据"""
        # 生成数据表名
        table_name: str = "_".join(["tick", symbol.replace("-", "_"), exchange.value])

        # 查询数据条数
        self.cursor.execute(f"select count(*) from {table_name}")
        result: list = self.cursor.fetchall()
        count: int = int(result[0][0])

        # 删除tick数据
        self.cursor.execute(f"DROP TABLE {table_name}")

        return count


    def stmt2_batch(self, table_name: str, data_dct:dict[str: list], interval: Interval) -> None:
        """数据批量插入数据库 一个子表多行数据"""
        interval_str = interval.value
        stmt2 = self.get_tick_stmt2(interval=interval)
        if interval.name == Interval.TICK:
            table_info = TICK_TABLE
        else:
            table_info = BAR_TABLE

        if table_name.split("_")[0] == "bar":
            generate: Callable = generate_bar
        else:
            generate = generate_tick

        data: list[str] = [f"insert into {table_name} values"]
        count: int = 0

        for d in data_set:
            data.append(generate(d))
            count += 1

            if count == batch_size:
                self.cursor.execute(" ".join(data))

                data = [f"insert into {table_name} values"]
                count = 0

        if count != 0:
            self.cursor.execute(" ".join(data))

    def stmt2_query_vt_symbol(self, vt_symbol: str):
        # 查询单个股票的详细信息
        pass

    def stmt2_query(self):
        # sql = "select * from tick where vt_symbol in (?, ?, ?)"
        time_threshold = 1686844800000 
        sql = "select last(*) from tick where datetime >? partition by tbname"
        # vt_symbol = "300575.SZ"
        vt_symbols = [
            "300567.SZ",
            "300568.SZ",
            # "300569.SZ",
            "300575.SZ",
        ]
        t1 = time.time()
        # symbol, exchange = vt_symbol.split(".")
        with self.taosws_client.connection() as conn:
            conn.execute(f"use real_data")
            stmt2 = conn.stmt2_statement()
            stmt2.prepare("select last_row(*) from ? partition by tbname")
            # stmt2.prepare(sql)
            p_lst = []
            pyStmt2Param = taosws.stmt2_bind_param_view(
                table_name="tick",
                tags=None,
                columns=[
                        # taosws.millis_timestamps_to_column([time_threshold])
                    # taosws.varchar_to_column([vt_symbols[0]]),
                    # taosws.varchar_to_column([vt_symbols[1]]),
                    # taosws.varchar_to_column([vt_symbols[2]]),
                ]
            )
            p_lst.append(pyStmt2Param)
            stmt2.bind(p_lst)
            stmt2.execute()
            result = stmt2.result_set()
            
            t2 = time.time()
            print(result, t2 -t1)
        lst = [x for x in result]
        print(len(lst))
 
    def stmts_get_full_tick(self):
        f"select last_row(*) from {self.db_name}.tick partition by vt_symbol"

    def get_full_tick(self):
        sql = f"select last_row(*) from {self.db_name}.tick partition by vt_symbol" 
        t1 = time.time()
        res= []
        with self.connection() as conn:
            cursor = conn.cursor()
            r = cursor.execute(sql)
            res = cursor.fetch_all_into_dict()
            
        t2 = time.time()
        print(len(res), r, t2-t1)
    
    
    
    def close(self):
        if self.tick_stmt2:
            self.tick_stmt2.close()
        self.conn.close()
        self.taosws_client.close()

def generate_bar(bar: BarData) -> str:
    """将BarData转换为可存储的字符串"""
    result: str = (f"('{bar.datetime}', {bar.volume}, {bar.turnover}, {bar.open_interest},"
                   + f"{bar.open_price}, {bar.high_price}, {bar.low_price}, {bar.close_price})")

    return result


def generate_tick(tick: TickData) -> str:
    """将TickData转换为可存储的字符串"""
    # tick不带localtime
    if tick.localtime:
        localtime: datetime = tick.localtime
    # tick带localtime
    else:
        localtime = tick.datetime

    result: str = (f"('{tick.datetime}', '{tick.name}', {tick.volume}, {tick.turnover}, "
                   + f"{tick.open_interest}, {tick.last_price}, {tick.last_volume}, "
                   + f"{tick.limit_up}, {tick.limit_down}, {tick.open_price}, {tick.high_price}, {tick.low_price}, {tick.pre_close}, "
                   + f"{tick.bid_price_1}, {tick.bid_price_2}, {tick.bid_price_3}, {tick.bid_price_4}, {tick.bid_price_5}, "
                   + f"{tick.ask_price_1}, {tick.ask_price_2}, {tick.ask_price_3}, {tick.ask_price_4}, {tick.ask_price_5}, "
                   + f"{tick.bid_volume_1}, {tick.bid_volume_2}, {tick.bid_volume_3}, {tick.bid_volume_4}, {tick.bid_volume_5}, "
                   + f"{tick.ask_volume_1}, {tick.ask_volume_2}, {tick.ask_volume_3}, {tick.ask_volume_4}, {tick.ask_volume_5}, "
                   + f"'{localtime}')")

    return result



