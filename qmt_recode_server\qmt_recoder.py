import time
import datetime
import taosws
from queue import Empty, Queue
from threading import Thread
from xtquant import xtdata
from .taosws_base import TaoswsBase
from .taosws_script import DB_NAME, BAR_TABLE, TICK_TABLE

class RecodeServer:
    def __init__(self, config: dict):
        self._active = False
        self.full_tick = {}
        self.db_client = TaoswsBase(
            host=config["host"],
            port=config["port"],
            user=config["user"],
            password=config["password"],
        )
        self.db_name = DB_NAME
        self.tick_name = TICK_TABLE["name"]
        self.conn = self.init_db()
        self.tick_stmt2 = self.get_tick_stmt2()
        
    def init_db(self):
        conn = self.db_client.create_connect()
        # with self.taosws_client.connection() as conn:
        conn.execute(f"CREATE DATABASE IF NOT EXISTS {self.db_name} KEEP 36500")
        conn.execute(f"use {self.db_name}")
        cursor = conn.cursor()
        cursor.execute(f"use {self.db_name}")
        # bar 超级表
        table_names = BAR_TABLE["names"]
        col_defs = [f"{name} {type_}" for name, type_ in BAR_TABLE["keys"].items()]
        tag_defs = [f"{name} {type_}" for name, type_ in BAR_TABLE["tags"].items()]
        for t_name in table_names:
            sql = f"CREATE STABLE IF NOT EXISTS {t_name} ({','.join(col_defs)}) TAGS ({','.join(tag_defs)})"
            cursor.execute(sql)
        # tick 超级表
        table_name = TICK_TABLE["name"]
        col_defs = [f"{name} {type_}" for name, type_ in TICK_TABLE["keys"].items()]
        tag_defs = [f"{name} {type_}" for name, type_ in TICK_TABLE["tags"].items()]
        sql = f"CREATE STABLE IF NOT EXISTS {table_name} ({','.join(col_defs)}) TAGS ({','.join(tag_defs)})"
        cursor.execute(sql)
        cursor.close()
        return conn
    

    def get_tick_stmt2(self):
        sql = f"insert into ? using {TICK_TABLE["name"]} tags ({','.join(['?' for _ in TICK_TABLE['tags']])}) values ({','.join(['?' for _ in TICK_TABLE['keys']])})"
        tick_stmt2 = self.conn.stmt2_statement()
        tick_stmt2.prepare(sql)
        return tick_stmt2
    
    def get_stmt2param_tick(self, vt_symbol: str, d:dict):
        "同一个子表的数据"
        symbol, market = vt_symbol.split(".")
        table_name = f"{self.tick_name}_{symbol}_{market.lower()}"
        tags = [
                taosws.varchar_to_tag(vt_symbol),
                taosws.varchar_to_tag(symbol),
                taosws.varchar_to_tag(market),
            ]
        stmt2param = taosws.stmt2_bind_param_view(
            table_name=table_name, 
            tags=tags,
            columns=[
                taosws.millis_timestamps_to_column([d.get("time", 0)]),
                taosws.doubles_to_column([round(d["lastPrice"], 3)]),
                taosws.doubles_to_column([data.get("time", 0)]),
            ]
        )
        tags = None
        col_dct = {}

                
            for k in TICK_TABLE["keys"]:
                if k == "datetime":
                    v = tick.timestamp
                elif k == "direction":
                    v = tick.direction.value
                else:
                    v = getattr(tick, k)
                col_dct.setdefault(k, []).append(v)
        columns = []
        for k, col in col_dct.items():
            if k == "datetime":
                columns.append(
                    
                )
            else:
                columns.append(
                    taosws.doubles_to_column(col)
                )
        
        return stmt2param


    def save_tick_data(self):
        pass
    
    def recoder_tick(self):
        while self._active:
            try:
                ticks = self.recoder_tick_queue.get(block=True, timeout=1)
                self.db_client.save_tick_data(ticks=ticks)
            except Empty:
                pass

    def on_tick(self, data: dict):
        if not self._active:
            return
        for vt_symbol, d in data.items():
            symbol, exchange = vt_symbol.split(".")
            timestamp=d["time"]
            bp_data: list = d["bidPrice"]
            ap_data: list = d["askPrice"]
            bv_data: list = d["bidVol"]
            av_data: list = d["askVol"]
            tick: TickData = TickData(
                    symbol=symbol,
                    exchange=exchange,
                    datetime=generate_datetime(timestamp),
                    timestamp=timestamp,
                    last_price=round(d["lastPrice"], 3),
                    open=round(d["open"], 3),
                    high=round(d["high"], 3),
                    low=round(d["low"], 3),
                    pre_close=round(d["lastClose"], 3),
                    volume=d["volume"],
                    amount=d["amount"],
                    bid_price_1=round(bp_data[0], 3),
                    bid_price_2=round(bp_data[1], 3),
                    bid_price_3=round(bp_data[2], 3),
                    bid_price_4=round(bp_data[3], 3),
                    bid_price_5=round(bp_data[4], 3),
                    ask_price_1=round(ap_data[0], 3),
                    ask_price_2=round(ap_data[1], 3),
                    ask_price_3=round(ap_data[2], 3),
                    ask_price_4=round(ap_data[3], 3),
                    ask_price_5=round(ap_data[4], 3),
                    bid_volume_1=bv_data[0],
                    bid_volume_2=bv_data[1],
                    bid_volume_3=bv_data[2],
                    bid_volume_4=bv_data[3],
                    bid_volume_5=bv_data[4],
                    ask_volume_1=av_data[0],
                    ask_volume_2=av_data[1],
                    ask_volume_3=av_data[2],
                    ask_volume_4=av_data[3],
                    ask_volume_5=av_data[4],
                )
            self.extarp_tick(tick)
            ticks.append(tick)
            self.full_tick[vt_symbol] = tick
        self.recoder_tick_queue.put(ticks)
        self.other_tick_queue.put(ticks)
        # self.db_client.save_tick_data(ticks=ticks)

    def strart(self):
        # subscribe_whole_quote
        stock_list  = self.get_stock_list()
        xtdata.subscribe_whole_quote(
            code_list=stock_list,
            callback=self.on_tick
        )
        time.sleep(60)
        self._active = True

    def close(self):
        self._active = False
        time.sleep(60)
        for t in self.thread_task:
            t.join()

        self.db_client.close()

if __name__ == "__main__":
    qmt_server = RecodeServer()
    # qmt_server.strart()
    # qmt_server.test_scp()
    
    # qmt_server.test_strart()



