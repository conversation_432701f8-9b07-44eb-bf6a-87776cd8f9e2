"""
API依赖注入
"""

from typing import Optional

from fastapi import Depends, HTTPException, status
from loguru import logger

from app.services.market_data import MarketDataService
from app.services.subscription import SubscriptionService
from config.settings import Settings, get_settings


def get_market_data_service() -> MarketDataService:
    """获取市场数据服务实例"""
    return MarketDataService()


def get_subscription_service() -> Optional[SubscriptionService]:
    """获取订阅服务实例"""
    # 避免循环导入，使用延迟导入
    try:
        import sys
        if 'main' in sys.modules:
            main_module = sys.modules['main']
            if hasattr(main_module, 'background_manager'):
                return main_module.background_manager.get_subscription_service()
        logger.warning("后台任务管理器未初始化")
        return None
    except Exception as e:
        logger.warning(f"无法获取订阅服务实例: {e}")
        return None


def validate_symbol_and_exchange(symbol: str, exchange: str):
    """验证股票代码和交易所代码"""
    if not symbol or not symbol.strip():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="股票代码不能为空"
        )
    
    if not exchange or not exchange.strip():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="交易所代码不能为空"
        )
    
    # 验证交易所代码格式
    valid_exchanges = ["SH", "SZ", "BJ"]  # 上海、深圳、北京
    if exchange.upper() not in valid_exchanges:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"无效的交易所代码，支持的交易所: {', '.join(valid_exchanges)}"
        )


def validate_symbols_limit(symbols: list, settings: Settings = Depends(get_settings)):
    """验证股票代码数量限制"""
    if symbols and len(symbols) > settings.MAX_QUERY_SYMBOLS:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"单次查询股票数量不能超过{settings.MAX_QUERY_SYMBOLS}个"
        )
