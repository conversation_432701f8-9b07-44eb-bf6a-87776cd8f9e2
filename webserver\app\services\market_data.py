"""
市场数据服务
提供股票成交信息和K线数据的获取服务
"""

import asyncio
import random
from datetime import datetime, timedelta
from typing import Dict, List, Optional

from loguru import logger

from app.models.response import TickDataResponse, KlineDataResponse
from config.settings import get_settings


class MarketDataService:
    """市场数据服务类"""
    
    def __init__(self):
        self.settings = get_settings()
        self._tick_cache: Dict[str, TickDataResponse] = {}
        self._kline_cache: Dict[str, List[KlineDataResponse]] = {}
        
        # 模拟股票列表
        self._mock_symbols = [
            ("000001", "SZ", "平安银行"),
            ("000002", "SZ", "万科A"),
            ("600000", "SH", "浦发银行"),
            ("600036", "SH", "招商银行"),
            ("600519", "SH", "贵州茅台"),
            ("000858", "SZ", "五粮液"),
            ("002415", "SZ", "海康威视"),
            ("300059", "SZ", "东方财富"),
        ]
    
    async def get_single_tick_data(self, symbol: str, exchange: str) -> Optional[TickDataResponse]:
       pass
    
    async def get_multiple_tick_data(self, symbols: List[str], exchanges: List[str]) -> List[TickDataResponse]:
       pass
    
    async def get_single_kline_data(
        self,
        symbol: str,
        exchange: str,
        interval: str = "1m",
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        limit: int = 100
    ) -> List[KlineDataResponse]:
        """
        获取单个股票的K线信息
        
        Args:
            symbol: 股票代码
            exchange: 交易所代码
            interval: K线周期
            start_time: 开始时间
            end_time: 结束时间
            limit: 数据条数限制
            
        Returns:
            K线数据列表
        """
        try:
            logger.info(f"获取股票K线信息: {symbol}.{exchange}, 周期: {interval}")
            
            # 伪代码实现 - 模拟数据获取
            vt_symbol = f"{symbol}.{exchange}"
            cache_key = f"{vt_symbol}_{interval}"
            
            # 模拟数据获取延迟
            await asyncio.sleep(0.02)
            
            # 生成模拟K线数据
            kline_data = self._generate_mock_kline_data(
                symbol, exchange, interval, start_time, end_time, limit
            )
            
            # 更新缓存
            self._kline_cache[cache_key] = kline_data
            
            logger.debug(f"成功获取股票K线信息: {symbol}.{exchange}, 数据条数: {len(kline_data)}")
            return kline_data
            
        except Exception as e:
            logger.error(f"获取股票K线信息失败: {symbol}.{exchange}, 错误: {e}")
            return []
    
    async def get_multiple_kline_data(
        self,
        symbols: List[str],
        exchanges: List[str],
        interval: str = "1m",
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        limit: int = 100
    ) -> List[Dict]:
        """
        获取多个股票的K线信息
        
        Args:
            symbols: 股票代码列表，如果为空则获取所有股票
            exchanges: 交易所代码列表
            interval: K线周期
            start_time: 开始时间
            end_time: 结束时间
            limit: 每个股票的数据条数限制
            
        Returns:
            包含股票K线信息的字典列表
        """
        try:
            logger.info(f"获取多个股票K线信息，数量: {len(symbols) if symbols else '全部'}")
            
            # 如果symbols为空，获取所有模拟股票
            if not symbols:
                symbols = [item[0] for item in self._mock_symbols]
                exchanges = [item[1] for item in self._mock_symbols]
            
            # 确保exchanges长度匹配
            if len(exchanges) != len(symbols):
                exchanges = ["SZ" if symbol.startswith(("000", "002", "300")) else "SH" for symbol in symbols]
            
            # 并发获取数据
            tasks = []
            for symbol, exchange in zip(symbols, exchanges):
                task = self.get_single_kline_data(symbol, exchange, interval, start_time, end_time, limit)
                tasks.append(task)
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 组织返回数据
            kline_data_list = []
            for i, result in enumerate(results):
                if isinstance(result, list) and result:
                    symbol = symbols[i]
                    exchange = exchanges[i]
                    name = self._get_stock_name(symbol, exchange)
                    
                    kline_data_list.append({
                        "symbol": symbol,
                        "exchange": exchange,
                        "name": name,
                        "interval": interval,
                        "klines": [kline.model_dump() for kline in result]
                    })
                elif isinstance(result, Exception):
                    logger.warning(f"获取股票K线数据异常: {symbols[i]}.{exchanges[i]}, {result}")
            
            logger.info(f"成功获取 {len(kline_data_list)} 个股票K线信息")
            return kline_data_list

        except Exception as e:
            logger.error(f"获取多个股票K线信息失败: {e}")
            return []

    def _generate_mock_tick_data(self, symbol: str, exchange: str) -> TickDataResponse:
        """生成模拟tick数据"""
        now = datetime.now()
        timestamp = int(now.timestamp() * 1000)

        # 基础价格（根据股票代码生成）
        base_price = 10.0 + (hash(symbol) % 100)

        # 生成随机价格波动
        price_change = random.uniform(-0.5, 0.5)
        last_price = round(base_price + price_change, 2)
        pre_close = round(base_price, 2)

        # 生成其他价格
        open_price = round(pre_close + random.uniform(-0.3, 0.3), 2)
        high_price = round(max(last_price, open_price) + random.uniform(0, 0.2), 2)
        low_price = round(min(last_price, open_price) - random.uniform(0, 0.2), 2)

        # 生成买卖盘数据
        bid_prices = [round(last_price - 0.01 * (i + 1), 2) for i in range(5)]
        ask_prices = [round(last_price + 0.01 * (i + 1), 2) for i in range(5)]
        bid_volumes = [random.randint(100, 10000) for _ in range(5)]
        ask_volumes = [random.randint(100, 10000) for _ in range(5)]

        # 获取股票名称
        name = self._get_stock_name(symbol, exchange)

        # 计算涨跌额和涨跌幅
        change = round(last_price - pre_close, 2) if pre_close > 0 else 0.0
        change_percent = round((change / pre_close) * 100, 2) if pre_close > 0 else 0.0

        return TickDataResponse(
            symbol=symbol,
            exchange=exchange,
            name=name,
            datetime=now,
            timestamp=timestamp,
            last_price=last_price,
            open=open_price,
            high=high_price,
            low=low_price,
            pre_close=pre_close,
            volume=random.randint(100000, 10000000),
            amount=random.randint(1000000, 100000000),
            net_volume=random.randint(-50000, 50000),
            net_amount=random.randint(-5000000, 5000000),
            bid_price_1=bid_prices[0],
            bid_price_2=bid_prices[1],
            bid_price_3=bid_prices[2],
            bid_price_4=bid_prices[3],
            bid_price_5=bid_prices[4],
            ask_price_1=ask_prices[0],
            ask_price_2=ask_prices[1],
            ask_price_3=ask_prices[2],
            ask_price_4=ask_prices[3],
            ask_price_5=ask_prices[4],
            bid_volume_1=bid_volumes[0],
            bid_volume_2=bid_volumes[1],
            bid_volume_3=bid_volumes[2],
            bid_volume_4=bid_volumes[3],
            bid_volume_5=bid_volumes[4],
            ask_volume_1=ask_volumes[0],
            ask_volume_2=ask_volumes[1],
            ask_volume_3=ask_volumes[2],
            ask_volume_4=ask_volumes[3],
            ask_volume_5=ask_volumes[4],
            change=change,
            change_percent=change_percent,
        )

    def _generate_mock_kline_data(
        self,
        symbol: str,
        exchange: str,
        interval: str,
        start_time: Optional[datetime],
        end_time: Optional[datetime],
        limit: int
    ) -> List[KlineDataResponse]:
        """生成模拟K线数据"""
        klines = []

        # 确定时间范围
        if not end_time:
            end_time = datetime.now()
        if not start_time:
            # 根据interval和limit计算开始时间
            interval_minutes = self._get_interval_minutes(interval)
            start_time = end_time - timedelta(minutes=interval_minutes * limit)

        # 基础价格
        base_price = 10.0 + (hash(symbol) % 100)
        current_price = base_price

        # 生成K线数据
        current_time = start_time
        interval_minutes = self._get_interval_minutes(interval)

        count = 0
        while current_time <= end_time and count < limit:
            # 生成OHLC数据
            open_price = current_price
            close_price = round(open_price + random.uniform(-0.5, 0.5), 2)
            high_price = round(max(open_price, close_price) + random.uniform(0, 0.3), 2)
            low_price = round(min(open_price, close_price) - random.uniform(0, 0.3), 2)
            pre_close_price = round(current_price, 2)

            # 计算涨跌额和涨跌幅
            change = round(close_price - pre_close_price, 2) if pre_close_price > 0 else 0.0
            change_percent = round((change / pre_close_price) * 100, 2) if pre_close_price > 0 else 0.0

            kline = KlineDataResponse(
                symbol=symbol,
                exchange=exchange,
                datetime=current_time,
                timestamp=int(current_time.timestamp() * 1000),
                interval=interval,
                open=open_price,
                high=high_price,
                low=low_price,
                close=close_price,
                pre_close=pre_close_price,
                volume=random.randint(10000, 1000000),
                amount=random.randint(100000, 10000000),
                change=change,
                change_percent=change_percent,
            )

            klines.append(kline)
            current_price = close_price
            current_time += timedelta(minutes=interval_minutes)
            count += 1

        return klines

    def _get_interval_minutes(self, interval: str) -> int:
        """获取K线周期对应的分钟数"""
        interval_map = {
            "1m": 1,
            "5m": 5,
            "15m": 15,
            "30m": 30,
            "1h": 60,
            "4h": 240,
            "1d": 1440,
            "1w": 10080,
            "1M": 43200,  # 约30天
        }
        return interval_map.get(interval, 1)

    def _get_stock_name(self, symbol: str, exchange: str) -> str:
        """获取股票名称"""
        for mock_symbol, mock_exchange, name in self._mock_symbols:
            if mock_symbol == symbol and mock_exchange == exchange:
                return name
        return f"股票{symbol}"
