"""
API请求模型定义
"""

from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, Field, field_validator, model_validator


class TickQueryRequest(BaseModel):
    """单个股票成交信息查询请求"""
    
    symbol: str = Field(..., description="股票代码", example="000001")
    exchange: str = Field(..., description="交易所代码", example="SZ")
    
    @field_validator('symbol')
    @classmethod
    def validate_symbol(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('股票代码不能为空')
        return v.strip().upper()

    @field_validator('exchange')
    @classmethod
    def validate_exchange(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('交易所代码不能为空')
        return v.strip().upper()


class KlineQueryRequest(BaseModel):
    """单个股票K线信息查询请求"""
    
    symbol: str = Field(..., description="股票代码", example="000001")
    exchange: str = Field(..., description="交易所代码", example="SZ")
    interval: str = Field(
        default="1m",
        description="K线周期",
        example="1m"
    )
    start_time: Optional[datetime] = Field(
        None,
        description="开始时间"
    )
    end_time: Optional[datetime] = Field(
        None,
        description="结束时间"
    )
    limit: int = Field(
        default=100,
        ge=1,
        le=1000,
        description="返回数据条数限制"
    )
    
    @field_validator('symbol')
    @classmethod
    def validate_symbol(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('股票代码不能为空')
        return v.strip().upper()

    @field_validator('exchange')
    @classmethod
    def validate_exchange(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('交易所代码不能为空')
        return v.strip().upper()

    @field_validator('interval')
    @classmethod
    def validate_interval(cls, v):
        valid_intervals = ['1m', '5m', '15m', '30m', '1h', '4h', '1d', '1w', '1M']
        if v not in valid_intervals:
            raise ValueError(f'无效的K线周期，支持的周期: {", ".join(valid_intervals)}')
        return v


class MultiSymbolRequest(BaseModel):
    """多个股票查询请求"""
    
    symbols: List[str] = Field(
        default=[],
        description="股票代码列表",
        example=["000001", "000002", "600000"]
    )
    exchanges: List[str] = Field(
        default=[],
        description="交易所代码列表",
        example=["SZ", "SZ", "SH"]
    )
    
    @field_validator('symbols')
    @classmethod
    def validate_symbols(cls, v):
        if not v:
            # 如果为空，表示查询所有股票
            return v

        # 限制查询数量
        if len(v) > 100:
            raise ValueError('单次查询股票数量不能超过100个')

        # 清理和标准化股票代码
        cleaned_symbols = []
        for symbol in v:
            if symbol and symbol.strip():
                cleaned_symbols.append(symbol.strip().upper())

        return cleaned_symbols

    @model_validator(mode='after')
    def validate_exchanges_match_symbols(self):
        """验证交易所代码与股票代码匹配"""
        symbols = self.symbols
        exchanges = self.exchanges

        # 如果symbols为空，exchanges也应该为空
        if not symbols:
            self.exchanges = []
            return self

        # 如果提供了exchanges，数量必须与symbols匹配
        if exchanges and len(exchanges) != len(symbols):
            raise ValueError('交易所代码数量必须与股票代码数量匹配')

        # 如果没有提供exchanges，默认为空列表
        if not exchanges:
            self.exchanges = []
            return self

        # 清理和标准化交易所代码
        cleaned_exchanges = []
        for exchange in exchanges:
            if exchange and exchange.strip():
                cleaned_exchanges.append(exchange.strip().upper())
            else:
                cleaned_exchanges.append("")

        self.exchanges = cleaned_exchanges
        return self


class MultiKlineRequest(MultiSymbolRequest):
    """多个股票K线查询请求"""
    
    interval: str = Field(
        default="1m",
        description="K线周期",
        example="1m"
    )
    start_time: Optional[datetime] = Field(
        None,
        description="开始时间"
    )
    end_time: Optional[datetime] = Field(
        None,
        description="结束时间"
    )
    limit: int = Field(
        default=100,
        ge=1,
        le=1000,
        description="每个股票返回数据条数限制"
    )
    
    @field_validator('interval')
    @classmethod
    def validate_interval(cls, v):
        valid_intervals = ['1m', '5m', '15m', '30m', '1h', '4h', '1d', '1w', '1M']
        if v not in valid_intervals:
            raise ValueError(f'无效的K线周期，支持的周期: {", ".join(valid_intervals)}')
        return v
