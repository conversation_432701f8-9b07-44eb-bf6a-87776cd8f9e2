"""
后台任务管理器
负责管理应用的后台任务，包括实时数据订阅等
"""

import asyncio
from typing import Optional

from loguru import logger

from app.services.subscription import SubscriptionService
from config.settings import get_settings


class BackgroundTaskManager:
    """后台任务管理器"""
    
    def __init__(self):
        self.settings = get_settings()
        self._subscription_service: Optional[SubscriptionService] = None
        self._is_running = False
    
    async def start(self):
        """启动后台任务"""
        if self._is_running:
            logger.warning("后台任务管理器已经在运行中")
            return
        
        logger.info("启动后台任务管理器")
        self._is_running = True
        
        try:
            # 启动订阅服务
            self._subscription_service = SubscriptionService()
            await self._subscription_service.start()
            
            # 添加默认订阅
            if self.settings.TICK_SUBSCRIPTION_ENABLED:
                self._subscription_service.add_default_subscriptions()
            
            logger.info("后台任务管理器启动完成")
            
        except Exception as e:
            logger.error(f"启动后台任务管理器失败: {e}")
            await self.stop()
            raise
    
    async def stop(self):
        """停止后台任务"""
        if not self._is_running:
            return
        
        logger.info("停止后台任务管理器")
        self._is_running = False
        
        try:
            # 停止订阅服务
            if self._subscription_service:
                await self._subscription_service.stop()
                self._subscription_service = None
            
            logger.info("后台任务管理器已停止")
            
        except Exception as e:
            logger.error(f"停止后台任务管理器异常: {e}")
    
    def get_subscription_service(self) -> Optional[SubscriptionService]:
        """获取订阅服务实例"""
        return self._subscription_service
    
    def is_running(self) -> bool:
        """检查后台任务是否正在运行"""
        return self._is_running
    
    async def health_check(self) -> dict:
        """健康检查"""
        health_status = {
            "manager_running": self._is_running,
            "subscription_service": False,
            "subscribed_symbols_count": 0,
        }
        
        if self._subscription_service:
            health_status["subscription_service"] = self._subscription_service.is_running
            health_status["subscribed_symbols_count"] = len(self._subscription_service.get_subscribed_symbols())
        
        return health_status
