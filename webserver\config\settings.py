"""
应用配置管理
"""

import os
import yaml
from functools import lru_cache
from pathlib import Path
from typing import List, Optional

from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """应用配置类"""
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=True
    )
    
    # 基础配置
    APP_NAME: str = "金融市场数据服务"
    VERSION: str = "1.0.0"
    DEBUG: bool = Field(default=False, description="调试模式")
    
    # 服务器配置
    HOST: str = Field(default="0.0.0.0", description="服务器主机")
    PORT: int = Field(default=8000, description="服务器端口")
    
    # CORS配置
    ALLOWED_ORIGINS: List[str] = Field(
        default=["*"],
        description="允许的跨域来源"
    )
    
    # 日志配置
    LOG_LEVEL: str = Field(default="INFO", description="日志级别")
    LOG_FILE: str = Field(default="logs/app.log", description="日志文件路径")
    LOG_ROTATION: str = Field(default="1 day", description="日志轮转周期")
    LOG_RETENTION: str = Field(default="30 days", description="日志保留时间")
    
    # 数据源配置
    DATA_SOURCE_TYPE: str = Field(default="mock", description="数据源类型")
    
    # 实时数据配置
    TICK_SUBSCRIPTION_ENABLED: bool = Field(
        default=True,
        description="是否启用实时tick数据订阅"
    )
    TICK_UPDATE_INTERVAL: float = Field(
        default=0.1,
        description="tick数据更新间隔(秒)"
    )
    
    # 缓存配置
    CACHE_TTL: int = Field(default=60, description="缓存过期时间(秒)")
    MAX_CACHE_SIZE: int = Field(default=1000, description="最大缓存条目数")
    
    # API配置
    API_V1_PREFIX: str = Field(default="/api/v1", description="API v1前缀")
    MAX_QUERY_SYMBOLS: int = Field(
        default=100,
        description="单次查询最大股票数量"
    )
    
    # 数据库配置（预留）
    DATABASE_URL: str = Field(
        default="sqlite:///./market_data.db",
        description="数据库连接URL"
    )
    
    def get_log_config(self) -> dict:
        """获取日志配置"""
        return {
            "level": self.LOG_LEVEL,
            "file": self.LOG_FILE,
            "rotation": self.LOG_ROTATION,
            "retention": self.LOG_RETENTION,
        }


def load_config_from_yaml(config_file: str = "config/config.yaml") -> dict:
    """从YAML文件加载配置"""
    config_path = Path(config_file)

    if not config_path.exists():
        return {}

    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config_data = yaml.safe_load(f)

        # 获取环境变量指定的环境，默认为development
        env = os.getenv("APP_ENV", "development")

        # 返回对应环境的配置
        return config_data.get(env, {})

    except Exception as e:
        print(f"加载配置文件失败: {e}")
        return {}


@lru_cache()
def get_settings() -> Settings:
    """获取配置实例（单例模式）"""
    # 加载YAML配置
    yaml_config = load_config_from_yaml()

    # 合并YAML配置到环境变量配置中
    settings = Settings()

    # 如果YAML配置中有值，则覆盖默认值
    if yaml_config:
        for key, value in yaml_config.items():
            if hasattr(settings, key.upper()):
                setattr(settings, key.upper(), value)

    return settings
