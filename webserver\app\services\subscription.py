"""
实时数据订阅服务
负责管理full_tick实时数据的订阅和更新
"""

import asyncio
from datetime import datetime
from typing import Dict, Set, Optional, Callable

from loguru import logger

from app.models.response import TickDataResponse
from app.services.market_data import MarketDataService
from config.settings import get_settings


class SubscriptionService:
    """实时数据订阅服务类"""
    
    def __init__(self):
        self.settings = get_settings()
        self.market_data_service = MarketDataService()
        
        # 订阅管理
        self._subscribed_symbols: Set[str] = set()
        self._full_tick_data: Dict[str, TickDataResponse] = {}
        self._subscribers: Dict[str, Set[Callable]] = {}
        
        # 任务管理
        self._subscription_task: Optional[asyncio.Task] = None
        self._is_running = False
        
        # 配置
        self._update_interval = self.settings.TICK_UPDATE_INTERVAL
        
    async def start(self):
        """启动订阅服务"""
        if self._is_running:
            logger.warning("订阅服务已经在运行中")
            return
        
        logger.info("启动实时数据订阅服务")
        self._is_running = True
        
        if self.settings.TICK_SUBSCRIPTION_ENABLED:
            # 启动后台订阅任务
            self._subscription_task = asyncio.create_task(self._subscription_loop())
            logger.info("实时tick数据订阅已启动")
        else:
            logger.info("实时tick数据订阅已禁用")
    
    async def stop(self):
        """停止订阅服务"""
        if not self._is_running:
            return
        
        logger.info("停止实时数据订阅服务")
        self._is_running = False
        
        if self._subscription_task:
            self._subscription_task.cancel()
            try:
                await self._subscription_task
            except asyncio.CancelledError:
                pass
            self._subscription_task = None
        
        # 清理数据
        self._subscribed_symbols.clear()
        self._full_tick_data.clear()
        self._subscribers.clear()
        
        logger.info("实时数据订阅服务已停止")
    
    def subscribe_symbol(self, symbol: str, exchange: str, callback: Optional[Callable] = None):
        """
        订阅股票实时数据
        
        Args:
            symbol: 股票代码
            exchange: 交易所代码
            callback: 数据更新回调函数
        """
        vt_symbol = f"{symbol}.{exchange}"
        
        if vt_symbol not in self._subscribed_symbols:
            self._subscribed_symbols.add(vt_symbol)
            logger.info(f"订阅股票实时数据: {vt_symbol}")
        
        # 注册回调函数
        if callback:
            if vt_symbol not in self._subscribers:
                self._subscribers[vt_symbol] = set()
            self._subscribers[vt_symbol].add(callback)
    
    def unsubscribe_symbol(self, symbol: str, exchange: str, callback: Optional[Callable] = None):
        """
        取消订阅股票实时数据
        
        Args:
            symbol: 股票代码
            exchange: 交易所代码
            callback: 要移除的回调函数
        """
        vt_symbol = f"{symbol}.{exchange}"
        
        # 移除回调函数
        if callback and vt_symbol in self._subscribers:
            self._subscribers[vt_symbol].discard(callback)
            if not self._subscribers[vt_symbol]:
                del self._subscribers[vt_symbol]
        
        # 如果没有回调函数了，取消订阅
        if vt_symbol not in self._subscribers:
            if vt_symbol in self._subscribed_symbols:
                self._subscribed_symbols.remove(vt_symbol)
                logger.info(f"取消订阅股票实时数据: {vt_symbol}")
            
            # 清理缓存数据
            if vt_symbol in self._full_tick_data:
                del self._full_tick_data[vt_symbol]
    
    def get_full_tick_data(self, symbol: str, exchange: str) -> Optional[TickDataResponse]:
        """
        获取full_tick实时数据
        
        Args:
            symbol: 股票代码
            exchange: 交易所代码
            
        Returns:
            实时tick数据，如果没有订阅返回None
        """
        vt_symbol = f"{symbol}.{exchange}"
        return self._full_tick_data.get(vt_symbol)
    
    def get_all_full_tick_data(self) -> Dict[str, TickDataResponse]:
        """获取所有订阅的full_tick数据"""
        return self._full_tick_data.copy()
    
    def get_subscribed_symbols(self) -> Set[str]:
        """获取已订阅的股票列表"""
        return self._subscribed_symbols.copy()
    
    def is_subscribed(self, symbol: str, exchange: str) -> bool:
        """检查是否已订阅某个股票"""
        vt_symbol = f"{symbol}.{exchange}"
        return vt_symbol in self._subscribed_symbols
    
    async def _subscription_loop(self):
        """订阅循环任务"""
        logger.info("开始实时数据订阅循环")
        
        try:
            while self._is_running:
                if self._subscribed_symbols:
                    await self._update_subscribed_data()
                
                # 等待下次更新
                await asyncio.sleep(self._update_interval)
                
        except asyncio.CancelledError:
            logger.info("订阅循环任务被取消")
            raise
        except Exception as e:
            logger.error(f"订阅循环任务异常: {e}")
            # 继续运行，不中断服务
    
    async def _update_subscribed_data(self):
        """更新订阅的数据"""
        try:
            # 解析订阅的股票代码
            symbols_to_update = []
            for vt_symbol in self._subscribed_symbols:
                symbol, exchange = vt_symbol.split(".")
                symbols_to_update.append((symbol, exchange))
            
            # 批量获取数据
            tasks = []
            for symbol, exchange in symbols_to_update:
                task = self.market_data_service.get_single_tick_data(symbol, exchange)
                tasks.append(task)
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 更新full_tick数据并通知订阅者
            for i, result in enumerate(results):
                if isinstance(result, TickDataResponse):
                    symbol, exchange = symbols_to_update[i]
                    vt_symbol = f"{symbol}.{exchange}"
                    
                    # 更新数据
                    old_data = self._full_tick_data.get(vt_symbol)
                    self._full_tick_data[vt_symbol] = result
                    
                    # 通知订阅者（如果数据有变化）
                    if self._data_changed(old_data, result):
                        await self._notify_subscribers(vt_symbol, result)
                
                elif isinstance(result, Exception):
                    symbol, exchange = symbols_to_update[i]
                    logger.warning(f"更新股票数据失败: {symbol}.{exchange}, {result}")
        
        except Exception as e:
            logger.error(f"更新订阅数据异常: {e}")
    
    def _data_changed(self, old_data: Optional[TickDataResponse], new_data: TickDataResponse) -> bool:
        """检查数据是否有变化"""
        if not old_data:
            return True
        
        # 比较关键字段
        return (
            old_data.last_price != new_data.last_price or
            old_data.volume != new_data.volume or
            old_data.timestamp != new_data.timestamp
        )
    
    async def _notify_subscribers(self, vt_symbol: str, data: TickDataResponse):
        """通知订阅者数据更新"""
        if vt_symbol in self._subscribers:
            for callback in self._subscribers[vt_symbol]:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(data)
                    else:
                        callback(data)
                except Exception as e:
                    logger.error(f"通知订阅者异常: {vt_symbol}, {e}")
    
    def add_default_subscriptions(self):
        """添加默认订阅（主要股票）"""
        default_symbols = [
            ("000001", "SZ"),  # 平安银行
            ("000002", "SZ"),  # 万科A
            ("600000", "SH"),  # 浦发银行
            ("600036", "SH"),  # 招商银行
            ("600519", "SH"),  # 贵州茅台
        ]
        
        for symbol, exchange in default_symbols:
            self.subscribe_symbol(symbol, exchange)
        
        logger.info(f"已添加 {len(default_symbols)} 个默认订阅")
    
    @property
    def is_running(self) -> bool:
        """检查服务是否正在运行"""
        return self._is_running
