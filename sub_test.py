import csv
import taosws
import datetime

def writer_csv(filename, data, header):
    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
            # 先写入表头
        writer.writerow(header)
        
        # 再写入所有数据行
        writer.writerows(data)

conf = {
    "td.connect.websocket.scheme": "ws",
    "td.connect.ip": "**************",           # 服务器地址
    "td.connect.port": "6041",              # WebSocket端口
    "td.connect.user": "root",              # 用户名
    "td.connect.pass": "taosdata",          # 密码
    "group.id": "tock_1",
    "auto.offset.reset": "latest",
}
consumer = taosws.Consumer(conf)
consumer.subscribe(["tick"])
header = ["vt_symbol", "datetime", "last_price", "high", "low", 
          "pre_close", "volume", "amount", "local_time"]
all_data = []
try:
    while True:
        # 拉取消息（超时1秒）
        message = consumer.poll(timeout=2.0)
        if not message:
            continue
        data_lst = []
        for block in message:
            data_lst.extend(
                 block.fetchall()
            )
        consumer.commit(message)
        now = datetime.datetime.now()
        for x in data_lst:
            if x[0] != "300575.SZ":
                continue
            x  = list(x)
            x.append(now)
            all_data.append(x)
        now = datetime.datetime.now()
        if now.minute == 30 and len(all_data) > 100:
            file_path = f"sub_{now.date()}.csv"
            writer_csv(file_path, all_data, header)
            all_data = []

except Exception as e:
    print(f"消费过程中出错: {e}")
finally:
    # 清理资源
    consumer.unsubscribe()
    consumer.close()

