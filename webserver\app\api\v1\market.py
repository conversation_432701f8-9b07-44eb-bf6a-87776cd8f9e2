"""
市场数据API路由
提供股票成交信息和K线数据的API接口
"""

from datetime import datetime
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from loguru import logger

from app.api.dependencies import (
    get_market_data_service,
    get_subscription_service,
    validate_symbol_and_exchange,
    validate_symbols_limit,
)
from app.models.request import (
    TickQueryRequest,
    KlineQueryRequest,
    MultiSymbolRequest,
    MultiKlineRequest,
)
from app.models.response import (
    ApiResponse,
    TickDataResponse,
    KlineDataResponse,
    MultiTickDataResponse,
    MultiKlineDataResponse,
)
from app.services.market_data import MarketDataService
from app.services.subscription import SubscriptionService


router = APIRouter()


@router.get(
    "/tick/{symbol}",
    response_model=ApiResponse,
    summary="获取单个股票成交信息",
    description="获取指定股票的实时成交信息，包括价格、成交量、买卖盘等数据"
)
async def get_single_tick(
    symbol: str,
    exchange: str = Query(..., description="交易所代码"),
    market_service: MarketDataService = Depends(get_market_data_service),
):
    """获取单个股票的成交信息"""
    try:
        # 验证参数
        validate_symbol_and_exchange(symbol, exchange)
        
        # 获取数据
        tick_data = await market_service.get_single_tick_data(symbol.upper(), exchange.upper())
        
        if not tick_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"未找到股票 {symbol}.{exchange} 的成交信息"
            )
        
        return ApiResponse.success(data=tick_data, message="获取股票成交信息成功")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取单个股票成交信息异常: {symbol}.{exchange}, {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取股票成交信息失败"
        )


@router.post(
    "/tick/multiple",
    response_model=ApiResponse,
    summary="获取多个股票成交信息",
    description="批量获取多个股票的成交信息，如果不指定股票列表则返回所有股票"
)
async def get_multiple_tick(
    request: MultiSymbolRequest,
    market_service: MarketDataService = Depends(get_market_data_service),
):
    """获取多个股票的成交信息"""
    try:
        # 验证参数
        validate_symbols_limit(request.symbols)
        
        # 获取数据
        tick_data_list = await market_service.get_multiple_tick_data(
            request.symbols, request.exchanges
        )
        
        response_data = MultiTickDataResponse(
            total=len(tick_data_list),
            data=tick_data_list
        )
        
        message = f"获取 {len(tick_data_list)} 个股票成交信息成功"
        return ApiResponse.success(data=response_data, message=message)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取多个股票成交信息异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取多个股票成交信息失败"
        )


@router.get(
    "/tick",
    response_model=ApiResponse,
    summary="获取所有股票成交信息",
    description="获取所有可用股票的成交信息"
)
async def get_all_tick(
    market_service: MarketDataService = Depends(get_market_data_service),
):
    """获取所有股票的成交信息"""
    try:
        # 获取所有股票数据（传入空列表）
        tick_data_list = await market_service.get_multiple_tick_data([], [])
        
        response_data = MultiTickDataResponse(
            total=len(tick_data_list),
            data=tick_data_list
        )
        
        message = f"获取所有股票成交信息成功，共 {len(tick_data_list)} 个"
        return ApiResponse.success(data=response_data, message=message)
        
    except Exception as e:
        logger.error(f"获取所有股票成交信息异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取所有股票成交信息失败"
        )


@router.get(
    "/kline/{symbol}",
    response_model=ApiResponse,
    summary="获取单个股票K线信息",
    description="获取指定股票的K线数据，支持多种时间周期"
)
async def get_single_kline(
    symbol: str,
    exchange: str = Query(..., description="交易所代码"),
    interval: str = Query(default="1m", description="K线周期"),
    start_time: Optional[datetime] = Query(None, description="开始时间"),
    end_time: Optional[datetime] = Query(None, description="结束时间"),
    limit: int = Query(default=100, ge=1, le=1000, description="数据条数限制"),
    market_service: MarketDataService = Depends(get_market_data_service),
):
    """获取单个股票的K线信息"""
    try:
        # 验证参数
        validate_symbol_and_exchange(symbol, exchange)
        
        # 获取数据
        kline_data = await market_service.get_single_kline_data(
            symbol.upper(), exchange.upper(), interval, start_time, end_time, limit
        )
        
        message = f"获取股票K线信息成功，共 {len(kline_data)} 条数据"
        return ApiResponse.success(data=kline_data, message=message)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取单个股票K线信息异常: {symbol}.{exchange}, {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取股票K线信息失败"
        )


@router.post(
    "/kline/multiple",
    response_model=ApiResponse,
    summary="获取多个股票K线信息",
    description="批量获取多个股票的K线数据，如果不指定股票列表则返回所有股票"
)
async def get_multiple_kline(
    request: MultiKlineRequest,
    market_service: MarketDataService = Depends(get_market_data_service),
):
    """获取多个股票的K线信息"""
    try:
        # 验证参数
        validate_symbols_limit(request.symbols)

        # 获取数据
        kline_data_list = await market_service.get_multiple_kline_data(
            request.symbols,
            request.exchanges,
            request.interval,
            request.start_time,
            request.end_time,
            request.limit
        )

        response_data = MultiKlineDataResponse(
            total=len(kline_data_list),
            data=kline_data_list
        )

        message = f"获取 {len(kline_data_list)} 个股票K线信息成功"
        return ApiResponse.success(data=response_data, message=message)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取多个股票K线信息异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取多个股票K线信息失败"
        )


@router.get(
    "/kline",
    response_model=ApiResponse,
    summary="获取所有股票K线信息",
    description="获取所有可用股票的K线数据"
)
async def get_all_kline(
    interval: str = Query(default="1m", description="K线周期"),
    start_time: Optional[datetime] = Query(None, description="开始时间"),
    end_time: Optional[datetime] = Query(None, description="结束时间"),
    limit: int = Query(default=100, ge=1, le=1000, description="每个股票数据条数限制"),
    market_service: MarketDataService = Depends(get_market_data_service),
):
    """获取所有股票的K线信息"""
    try:
        # 获取所有股票数据（传入空列表）
        kline_data_list = await market_service.get_multiple_kline_data(
            [], [], interval, start_time, end_time, limit
        )

        response_data = MultiKlineDataResponse(
            total=len(kline_data_list),
            data=kline_data_list
        )

        message = f"获取所有股票K线信息成功，共 {len(kline_data_list)} 个"
        return ApiResponse.success(data=response_data, message=message)

    except Exception as e:
        logger.error(f"获取所有股票K线信息异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取所有股票K线信息失败"
        )


@router.get(
    "/subscription/status",
    response_model=ApiResponse,
    summary="获取订阅状态",
    description="获取实时数据订阅服务的状态信息"
)
async def get_subscription_status(
    subscription_service: Optional[SubscriptionService] = Depends(get_subscription_service),
):
    """获取订阅状态"""
    try:
        if not subscription_service:
            return ApiResponse.success(
                data={
                    "enabled": False,
                    "running": False,
                    "subscribed_symbols": [],
                    "message": "订阅服务未启用"
                },
                message="订阅服务状态获取成功"
            )

        subscribed_symbols = list(subscription_service.get_subscribed_symbols())

        status_data = {
            "enabled": True,
            "running": subscription_service.is_running,
            "subscribed_symbols": subscribed_symbols,
            "subscribed_count": len(subscribed_symbols),
        }

        return ApiResponse.success(data=status_data, message="订阅服务状态获取成功")

    except Exception as e:
        logger.error(f"获取订阅状态异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取订阅状态失败"
        )


@router.get(
    "/subscription/data",
    response_model=ApiResponse,
    summary="获取订阅的实时数据",
    description="获取当前订阅的所有股票的实时tick数据"
)
async def get_subscription_data(
    subscription_service: Optional[SubscriptionService] = Depends(get_subscription_service),
):
    """获取订阅的实时数据"""
    try:
        if not subscription_service:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="订阅服务未启用"
            )

        # 获取所有订阅的实时数据
        full_tick_data = subscription_service.get_all_full_tick_data()
        tick_data_list = list(full_tick_data.values())

        response_data = MultiTickDataResponse(
            total=len(tick_data_list),
            data=tick_data_list
        )

        message = f"获取订阅实时数据成功，共 {len(tick_data_list)} 个"
        return ApiResponse.success(data=response_data, message=message)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取订阅实时数据异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取订阅实时数据失败"
        )


@router.post(
    "/subscription/subscribe",
    response_model=ApiResponse,
    summary="订阅股票实时数据",
    description="添加股票到实时数据订阅列表"
)
async def subscribe_symbol(
    symbol: str = Query(..., description="股票代码"),
    exchange: str = Query(..., description="交易所代码"),
    subscription_service: Optional[SubscriptionService] = Depends(get_subscription_service),
):
    """订阅股票实时数据"""
    try:
        if not subscription_service:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="订阅服务未启用"
            )

        # 验证参数
        validate_symbol_and_exchange(symbol, exchange)

        # 添加订阅
        subscription_service.subscribe_symbol(symbol.upper(), exchange.upper())

        result_data = {
            "symbol": symbol.upper(),
            "exchange": exchange.upper(),
            "subscribed": True
        }

        return ApiResponse.success(
            data=result_data,
            message=f"成功订阅股票 {symbol.upper()}.{exchange.upper()}"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"订阅股票异常: {symbol}.{exchange}, {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="订阅股票失败"
        )


@router.delete(
    "/subscription/unsubscribe",
    response_model=ApiResponse,
    summary="取消订阅股票实时数据",
    description="从实时数据订阅列表中移除股票"
)
async def unsubscribe_symbol(
    symbol: str = Query(..., description="股票代码"),
    exchange: str = Query(..., description="交易所代码"),
    subscription_service: Optional[SubscriptionService] = Depends(get_subscription_service),
):
    """取消订阅股票实时数据"""
    try:
        if not subscription_service:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="订阅服务未启用"
            )

        # 验证参数
        validate_symbol_and_exchange(symbol, exchange)

        # 取消订阅
        subscription_service.unsubscribe_symbol(symbol.upper(), exchange.upper())

        result_data = {
            "symbol": symbol.upper(),
            "exchange": exchange.upper(),
            "subscribed": False
        }

        return ApiResponse.success(
            data=result_data,
            message=f"成功取消订阅股票 {symbol.upper()}.{exchange.upper()}"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"取消订阅股票异常: {symbol}.{exchange}, {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="取消订阅股票失败"
        )
