#!/usr/bin/env python3
"""
调试启动脚本 - 用于诊断问题
"""

import sys
import os
from pathlib import Path

def check_environment():
    """检查环境"""
    print("🔍 检查环境...")
    print(f"Python版本: {sys.version}")
    print(f"当前工作目录: {os.getcwd()}")
    print(f"Python路径: {sys.path[:3]}...")  # 只显示前3个路径
    
    # 检查关键包
    packages = ['fastapi', 'pydantic', 'uvicorn', 'loguru']
    for package in packages:
        try:
            __import__(package)
            print(f"✓ {package} 已安装")
        except ImportError:
            print(f"✗ {package} 未安装")

def test_imports():
    """测试导入"""
    print("\n📦 测试导入...")
    
    try:
        print("导入datetime...")
        from datetime import datetime
        
        print("导入pydantic...")
        from pydantic import BaseModel, Field
        
        print("导入fastapi...")
        from fastapi import FastAPI
        
        print("导入项目模块...")
        from app.models.response import TickDataResponse, ApiResponse
        from app.models.request import TickQueryRequest
        from config.settings import get_settings
        
        print("✓ 所有导入成功")
        return True
        
    except Exception as e:
        print(f"✗ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_creation():
    """测试模型创建"""
    print("\n🏗️ 测试模型创建...")
    
    try:
        from datetime import datetime
        from app.models.response import TickDataResponse, ApiResponse
        
        # 创建tick数据
        tick = TickDataResponse(
            symbol="000001",
            exchange="SZ",
            datetime=datetime.now(),
            timestamp=1000,
            last_price=10.5,
            pre_close=10.0
        )
        
        print(f"✓ TickDataResponse: {tick.symbol}, 涨跌幅: {tick.change_percent}%")
        
        # 创建API响应
        response = ApiResponse.success(data=tick)
        print(f"✓ ApiResponse: {response.code}")
        
        # 测试序列化
        data = response.model_dump()
        print(f"✓ 序列化: {type(data)}")
        
        return True
        
    except Exception as e:
        print(f"✗ 模型创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_app_creation():
    """测试应用创建"""
    print("\n🚀 测试应用创建...")
    
    try:
        # 设置环境变量
        os.environ["APP_ENV"] = "testing"
        os.environ["TICK_SUBSCRIPTION_ENABLED"] = "false"
        os.environ["DEBUG"] = "true"
        
        from main import create_app
        
        app = create_app()
        print(f"✓ FastAPI应用创建成功: {app.title}")
        
        return True
        
    except Exception as e:
        print(f"✗ 应用创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧪 开始诊断...")
    print("=" * 50)
    
    # 添加项目根目录到Python路径
    project_root = Path(__file__).parent
    if str(project_root) not in sys.path:
        sys.path.insert(0, str(project_root))
    
    tests = [
        ("环境检查", check_environment),
        ("导入测试", test_imports),
        ("模型创建测试", test_model_creation),
        ("应用创建测试", test_app_creation),
    ]
    
    passed = 0
    total = len(tests)
    
    for name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {name} 通过")
            else:
                print(f"❌ {name} 失败")
        except Exception as e:
            print(f"❌ {name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"诊断结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！可以启动服务了！")
        
        # 尝试启动服务
        try:
            print("\n🚀 尝试启动服务...")
            import uvicorn
            from main import app
            
            print("服务即将启动在 http://localhost:8000")
            print("按 Ctrl+C 停止服务")
            
            uvicorn.run(app, host="127.0.0.1", port=8000, log_level="info")
            
        except KeyboardInterrupt:
            print("\n⏹️ 服务已停止")
        except Exception as e:
            print(f"\n❌ 启动服务失败: {e}")
    else:
        print("❌ 存在问题，需要修复后才能启动服务")

if __name__ == "__main__":
    main()
