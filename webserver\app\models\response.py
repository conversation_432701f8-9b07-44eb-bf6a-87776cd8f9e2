"""
API响应模型定义
"""

from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


class ApiResponse(BaseModel):
    """统一API响应格式"""

    code: int = Field(description="状态码", example=200)
    message: str = Field(description="响应消息", example="成功")
    data: Optional[Any] = Field(default=None, description="响应数据")
    timestamp: datetime = Field(
        default_factory=datetime.now,
        description="响应时间戳"
    )


    @classmethod
    def success(cls, data: Any = None, message: str = "成功") -> "ApiResponse":
        """创建成功响应"""
        return cls(code=200, message=message, data=data)

    @classmethod
    def error(cls, code: int = 500, message: str = "服务器内部错误", data: Any = None) -> "ApiResponse":
        """创建错误响应"""
        return cls(code=code, message=message, data=data)


class TickDataResponse(BaseModel):
    """股票成交信息响应数据"""

    pass


class KlineDataResponse(BaseModel):
    """K线数据响应"""

    pass


class MultiTickDataResponse(BaseModel):
    """多个股票成交信息响应"""
    
    total: int = Field(description="总数量")
    data: List[TickDataResponse] = Field(description="股票成交信息列表")


class MultiKlineDataResponse(BaseModel):
    """多个股票K线信息响应"""
    
    total: int = Field(description="总数量")
    data: List[Dict[str, Any]] = Field(description="股票K线信息列表")
    
    model_config = {
        "json_schema_extra": {
            "example": {
                "total": 2,
                "data": [
                    {
                        "symbol": "000001",
                        "exchange": "SZ",
                        "klines": [
                            {
                                "datetime": "2024-01-01T09:30:00",
                                "open": 10.0,
                                "high": 10.5,
                                "low": 9.8,
                                "close": 10.2,
                                "volume": 1000000
                            }
                        ]
                    }
                ]
            }
        }
    }
