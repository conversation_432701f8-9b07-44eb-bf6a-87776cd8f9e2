#!/usr/bin/env python3
"""
金融市场数据服务启动脚本
"""

import argparse
import os
import sys
import uvicorn
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config.settings import get_settings


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="金融市场数据服务")
    
    parser.add_argument(
        "--host",
        default=None,
        help="服务器主机地址"
    )
    
    parser.add_argument(
        "--port",
        type=int,
        default=None,
        help="服务器端口"
    )
    
    parser.add_argument(
        "--env",
        choices=["development", "production", "testing"],
        default="development",
        help="运行环境"
    )
    
    parser.add_argument(
        "--reload",
        action="store_true",
        help="启用自动重载（开发模式）"
    )
    
    parser.add_argument(
        "--workers",
        type=int,
        default=1,
        help="工作进程数量"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["debug", "info", "warning", "error"],
        default=None,
        help="日志级别"
    )
    
    return parser.parse_args()


def setup_environment(env: str):
    """设置环境变量"""
    os.environ["APP_ENV"] = env
    
    if env == "development":
        os.environ["DEBUG"] = "true"
        os.environ["LOG_LEVEL"] = "DEBUG"
    elif env == "production":
        os.environ["DEBUG"] = "false"
        os.environ["LOG_LEVEL"] = "INFO"
    elif env == "testing":
        os.environ["DEBUG"] = "true"
        os.environ["LOG_LEVEL"] = "DEBUG"
        os.environ["TICK_SUBSCRIPTION_ENABLED"] = "false"


def main():
    """主函数"""
    args = parse_args()
    
    # 设置环境
    setup_environment(args.env)
    
    # 获取配置
    settings = get_settings()
    
    # 确定运行参数
    host = args.host or settings.HOST
    port = args.port or settings.PORT
    reload = args.reload or (args.env == "development")
    log_level = args.log_level or settings.LOG_LEVEL.lower()
    
    print(f"🚀 启动金融市场数据服务")
    print(f"📍 环境: {args.env}")
    print(f"🌐 地址: http://{host}:{port}")
    print(f"📊 API文档: http://{host}:{port}/docs")
    print(f"📝 日志级别: {log_level}")
    print(f"🔄 自动重载: {'启用' if reload else '禁用'}")
    print(f"👥 工作进程: {args.workers}")
    print("-" * 50)
    
    # 启动服务器
    try:
        if args.workers > 1 and not reload:
            # 多进程模式
            uvicorn.run(
                "main:app",
                host=host,
                port=port,
                workers=args.workers,
                log_level=log_level,
                access_log=True,
            )
        else:
            # 单进程模式
            uvicorn.run(
                "main:app",
                host=host,
                port=port,
                reload=reload,
                log_level=log_level,
                access_log=True,
            )
    except KeyboardInterrupt:
        print("\n⏹️  服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
