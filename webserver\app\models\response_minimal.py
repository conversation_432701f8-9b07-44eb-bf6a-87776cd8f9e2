"""
最小化API响应模型定义 - 避免递归错误
"""

from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel


class ApiResponse(BaseModel):
    """统一API响应格式 - 最小化版本"""
    
    code: int = 200
    message: str = "成功"
    data: Optional[Any] = None
    timestamp: datetime = None
    
    def __init__(self, **data):
        if 'timestamp' not in data or data['timestamp'] is None:
            data['timestamp'] = datetime.now()
        super().__init__(**data)
    
    @classmethod
    def success(cls, data: Any = None, message: str = "成功") -> "ApiResponse":
        """创建成功响应"""
        return cls(code=200, message=message, data=data)
    
    @classmethod
    def error(cls, code: int = 500, message: str = "服务器内部错误", data: Any = None) -> "ApiResponse":
        """创建错误响应"""
        return cls(code=code, message=message, data=data)


class TickDataResponse(BaseModel):
    """股票成交信息响应数据 - 最小化版本"""
    
    # 基础信息
    symbol: str
    exchange: str
    name: str = ""
    datetime: datetime
    timestamp: int
    
    # 价格信息
    last_price: float = 0.0
    open: float = 0.0
    high: float = 0.0
    low: float = 0.0
    pre_close: float = 0.0
    
    # 成交信息
    volume: float = 0.0
    amount: float = 0.0
    net_volume: float = 0.0
    net_amount: float = 0.0
    
    # 买卖盘信息
    bid_price_1: float = 0.0
    bid_price_2: float = 0.0
    bid_price_3: float = 0.0
    bid_price_4: float = 0.0
    bid_price_5: float = 0.0
    
    ask_price_1: float = 0.0
    ask_price_2: float = 0.0
    ask_price_3: float = 0.0
    ask_price_4: float = 0.0
    ask_price_5: float = 0.0
    
    bid_volume_1: float = 0.0
    bid_volume_2: float = 0.0
    bid_volume_3: float = 0.0
    bid_volume_4: float = 0.0
    bid_volume_5: float = 0.0
    
    ask_volume_1: float = 0.0
    ask_volume_2: float = 0.0
    ask_volume_3: float = 0.0
    ask_volume_4: float = 0.0
    ask_volume_5: float = 0.0
    
    # 计算字段
    change: float = 0.0
    change_percent: float = 0.0


class KlineDataResponse(BaseModel):
    """K线数据响应 - 最小化版本"""
    
    symbol: str
    exchange: str
    datetime: datetime
    timestamp: int
    interval: str
    
    open: float = 0.0
    high: float = 0.0
    low: float = 0.0
    close: float = 0.0
    pre_close: float = 0.0
    volume: float = 0.0
    amount: float = 0.0
    
    # 计算字段
    change: float = 0.0
    change_percent: float = 0.0


class MultiTickDataResponse(BaseModel):
    """多个股票成交信息响应"""
    
    total: int
    data: List[TickDataResponse]


class MultiKlineDataResponse(BaseModel):
    """多个股票K线信息响应"""
    
    total: int
    data: List[Dict[str, Any]]
