"""
工具函数
"""

import re
from datetime import datetime, time
from typing import Optional


def format_datetime(dt: datetime, format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
    """格式化日期时间"""
    return dt.strftime(format_str)


def validate_stock_symbol(symbol: str) -> bool:
    """验证股票代码格式"""
    if not symbol:
        return False
    
    # 中国股票代码格式验证
    patterns = [
        r'^[0-9]{6}$',  # 6位数字
        r'^[A-Z]{1,5}$',  # 1-5位字母（港股等）
    ]
    
    for pattern in patterns:
        if re.match(pattern, symbol.upper()):
            return True
    
    return False


def validate_exchange_code(exchange: str) -> bool:
    """验证交易所代码"""
    if not exchange:
        return False
    
    valid_exchanges = {
        "SH", "SZ", "BJ",  # 中国大陆
        "HK", "HKEX",      # 香港
        "NYSE", "NASDAQ",  # 美国
    }
    
    return exchange.upper() in valid_exchanges


def calculate_change_percent(current: float, previous: float) -> float:
    """计算涨跌幅百分比"""
    if previous == 0:
        return 0.0
    
    return round(((current - previous) / previous) * 100, 2)


def format_number(value: float, decimal_places: int = 2) -> str:
    """格式化数字，添加千分位分隔符"""
    if value is None:
        return "0.00"
    
    format_str = f"{{:,.{decimal_places}f}}"
    return format_str.format(value)


def get_trading_day(dt: Optional[datetime] = None) -> datetime:
    """获取交易日（排除周末）"""
    if dt is None:
        dt = datetime.now()
    
    # 如果是周六，返回周五
    if dt.weekday() == 5:  # Saturday
        dt = dt.replace(day=dt.day - 1)
    # 如果是周日，返回周五
    elif dt.weekday() == 6:  # Sunday
        dt = dt.replace(day=dt.day - 2)
    
    return dt


def is_trading_time(dt: Optional[datetime] = None) -> bool:
    """判断是否在交易时间内（中国股市）"""
    if dt is None:
        dt = datetime.now()
    
    # 周末不交易
    if dt.weekday() >= 5:  # Saturday or Sunday
        return False
    
    current_time = dt.time()
    
    # 上午交易时间: 9:30-11:30
    morning_start = time(9, 30)
    morning_end = time(11, 30)
    
    # 下午交易时间: 13:00-15:00
    afternoon_start = time(13, 0)
    afternoon_end = time(15, 0)
    
    return (
        (morning_start <= current_time <= morning_end) or
        (afternoon_start <= current_time <= afternoon_end)
    )


def get_stock_market_by_symbol(symbol: str) -> str:
    """根据股票代码判断所属市场"""
    if not symbol:
        return "UNKNOWN"
    
    symbol = symbol.upper()
    
    # 上海证券交易所
    if symbol.startswith(('60', '68', '90')):
        return "SH"
    
    # 深圳证券交易所
    elif symbol.startswith(('00', '30', '20')):
        return "SZ"
    
    # 北京证券交易所
    elif symbol.startswith(('43', '83', '87')):
        return "BJ"
    
    # 默认返回深圳
    return "SZ"


def parse_interval_to_seconds(interval: str) -> int:
    """将时间间隔字符串转换为秒数"""
    interval_map = {
        "1m": 60,
        "5m": 300,
        "15m": 900,
        "30m": 1800,
        "1h": 3600,
        "4h": 14400,
        "1d": 86400,
        "1w": 604800,
        "1M": 2592000,  # 30天
    }
    
    return interval_map.get(interval, 60)


def safe_float(value, default: float = 0.0) -> float:
    """安全转换为浮点数"""
    try:
        return float(value) if value is not None else default
    except (ValueError, TypeError):
        return default


def safe_int(value, default: int = 0) -> int:
    """安全转换为整数"""
    try:
        return int(value) if value is not None else default
    except (ValueError, TypeError):
        return default


def truncate_string(text: str, max_length: int = 100, suffix: str = "...") -> str:
    """截断字符串"""
    if not text or len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix
