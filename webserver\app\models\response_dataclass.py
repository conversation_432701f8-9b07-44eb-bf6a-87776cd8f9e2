"""
使用dataclass的响应模型定义 - 避免Pydantic递归问题
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Any, Dict, List, Optional
import json


@dataclass
class ApiResponse:
    """统一API响应格式 - dataclass版本"""
    
    code: int = 200
    message: str = "成功"
    data: Optional[Any] = None
    timestamp: datetime = field(default_factory=datetime.now)
    
    @classmethod
    def success(cls, data: Any = None, message: str = "成功") -> "ApiResponse":
        """创建成功响应"""
        return cls(code=200, message=message, data=data)
    
    @classmethod
    def error(cls, code: int = 500, message: str = "服务器内部错误", data: Any = None) -> "ApiResponse":
        """创建错误响应"""
        return cls(code=code, message=message, data=data)
    
    def to_dict(self) -> dict:
        """转换为字典"""
        result = {
            "code": self.code,
            "message": self.message,
            "data": self.data,
            "timestamp": self.timestamp.isoformat() if isinstance(self.timestamp, datetime) else self.timestamp
        }
        return result


@dataclass
class TickDataResponse:
    """股票成交信息响应数据 - dataclass版本"""
    
    # 基础信息
    symbol: str
    exchange: str
    datetime: datetime
    timestamp: int
    name: str = ""
    
    # 价格信息
    last_price: float = 0.0
    open: float = 0.0
    high: float = 0.0
    low: float = 0.0
    pre_close: float = 0.0
    
    # 成交信息
    volume: float = 0.0
    amount: float = 0.0
    net_volume: float = 0.0
    net_amount: float = 0.0
    
    # 买卖盘信息
    bid_price_1: float = 0.0
    bid_price_2: float = 0.0
    bid_price_3: float = 0.0
    bid_price_4: float = 0.0
    bid_price_5: float = 0.0
    
    ask_price_1: float = 0.0
    ask_price_2: float = 0.0
    ask_price_3: float = 0.0
    ask_price_4: float = 0.0
    ask_price_5: float = 0.0
    
    bid_volume_1: float = 0.0
    bid_volume_2: float = 0.0
    bid_volume_3: float = 0.0
    bid_volume_4: float = 0.0
    bid_volume_5: float = 0.0
    
    ask_volume_1: float = 0.0
    ask_volume_2: float = 0.0
    ask_volume_3: float = 0.0
    ask_volume_4: float = 0.0
    ask_volume_5: float = 0.0
    
    # 计算字段
    change: float = 0.0
    change_percent: float = 0.0
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "symbol": self.symbol,
            "exchange": self.exchange,
            "name": self.name,
            "datetime": self.datetime.isoformat() if isinstance(self.datetime, datetime) else self.datetime,
            "timestamp": self.timestamp,
            "last_price": self.last_price,
            "open": self.open,
            "high": self.high,
            "low": self.low,
            "pre_close": self.pre_close,
            "volume": self.volume,
            "amount": self.amount,
            "net_volume": self.net_volume,
            "net_amount": self.net_amount,
            "bid_price_1": self.bid_price_1,
            "bid_price_2": self.bid_price_2,
            "bid_price_3": self.bid_price_3,
            "bid_price_4": self.bid_price_4,
            "bid_price_5": self.bid_price_5,
            "ask_price_1": self.ask_price_1,
            "ask_price_2": self.ask_price_2,
            "ask_price_3": self.ask_price_3,
            "ask_price_4": self.ask_price_4,
            "ask_price_5": self.ask_price_5,
            "bid_volume_1": self.bid_volume_1,
            "bid_volume_2": self.bid_volume_2,
            "bid_volume_3": self.bid_volume_3,
            "bid_volume_4": self.bid_volume_4,
            "bid_volume_5": self.bid_volume_5,
            "ask_volume_1": self.ask_volume_1,
            "ask_volume_2": self.ask_volume_2,
            "ask_volume_3": self.ask_volume_3,
            "ask_volume_4": self.ask_volume_4,
            "ask_volume_5": self.ask_volume_5,
            "change": self.change,
            "change_percent": self.change_percent,
        }


@dataclass
class KlineDataResponse:
    """K线数据响应 - dataclass版本"""
    
    symbol: str
    exchange: str
    datetime: datetime
    timestamp: int
    interval: str
    
    open: float = 0.0
    high: float = 0.0
    low: float = 0.0
    close: float = 0.0
    pre_close: float = 0.0
    volume: float = 0.0
    amount: float = 0.0
    
    # 计算字段
    change: float = 0.0
    change_percent: float = 0.0
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "symbol": self.symbol,
            "exchange": self.exchange,
            "datetime": self.datetime.isoformat() if isinstance(self.datetime, datetime) else self.datetime,
            "timestamp": self.timestamp,
            "interval": self.interval,
            "open": self.open,
            "high": self.high,
            "low": self.low,
            "close": self.close,
            "pre_close": self.pre_close,
            "volume": self.volume,
            "amount": self.amount,
            "change": self.change,
            "change_percent": self.change_percent,
        }


@dataclass
class MultiTickDataResponse:
    """多个股票成交信息响应"""
    
    total: int
    data: List[TickDataResponse]
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "total": self.total,
            "data": [item.to_dict() for item in self.data]
        }


@dataclass
class MultiKlineDataResponse:
    """多个股票K线信息响应"""
    
    total: int
    data: List[Dict[str, Any]]
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "total": self.total,
            "data": self.data
        }
