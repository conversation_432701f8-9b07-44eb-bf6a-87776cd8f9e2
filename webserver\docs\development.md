# 开发指南

## 开发环境设置

### 1. 环境要求

- Python 3.8+
- Git
- IDE（推荐 VS Code 或 PyCharm）

### 2. 项目设置

```bash
# 克隆项目
git clone <repository-url>
cd webserver

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows:
venv\Scripts\activate
# Linux/macOS:
source venv/bin/activate

# 安装开发依赖
pip install -r requirements.txt
pip install black isort flake8 pytest-cov

# 复制环境配置
cp .env.example .env
```

### 3. IDE 配置

#### VS Code

创建 `.vscode/settings.json`:

```json
{
    "python.defaultInterpreterPath": "./venv/bin/python",
    "python.formatting.provider": "black",
    "python.linting.enabled": true,
    "python.linting.flake8Enabled": true,
    "python.testing.pytestEnabled": true,
    "python.testing.pytestArgs": ["tests"],
    "editor.formatOnSave": true
}
```

## 代码规范

### 1. 代码风格

- 使用 Black 进行代码格式化
- 使用 isort 进行导入排序
- 使用 flake8 进行代码检查
- 遵循 PEP 8 规范

```bash
# 格式化代码
black .
isort .

# 检查代码风格
flake8 .
```

### 2. 命名规范

- **类名**: PascalCase（如 `MarketDataService`）
- **函数名**: snake_case（如 `get_tick_data`）
- **变量名**: snake_case（如 `tick_data`）
- **常量名**: UPPER_SNAKE_CASE（如 `MAX_QUERY_SYMBOLS`）
- **文件名**: snake_case（如 `market_data.py`）

### 3. 文档字符串

使用 Google 风格的文档字符串：

```python
def get_tick_data(symbol: str, exchange: str) -> Optional[TickData]:
    """获取股票成交信息.
    
    Args:
        symbol: 股票代码
        exchange: 交易所代码
        
    Returns:
        股票成交信息，如果不存在返回None
        
    Raises:
        ValueError: 当参数无效时
    """
    pass
```

## 架构设计

### 1. 分层架构

```
API层 (app/api/) 
    ↓
业务逻辑层 (app/services/)
    ↓
数据模型层 (app/models/)
    ↓
工具层 (app/utils/)
```

### 2. 依赖注入

使用 FastAPI 的依赖注入系统：

```python
from fastapi import Depends

def get_service() -> Service:
    return Service()

@router.get("/endpoint")
async def endpoint(service: Service = Depends(get_service)):
    return await service.do_something()
```

### 3. 异常处理

使用自定义异常类：

```python
from app.core.exceptions import MarketDataException

class CustomError(MarketDataException):
    def __init__(self, message: str):
        super().__init__(message, 400)

# 在业务逻辑中抛出
raise CustomError("自定义错误信息")
```

## 测试指南

### 1. 测试结构

```
tests/
├── conftest.py          # 测试配置和夹具
├── test_api.py          # API接口测试
├── test_services.py     # 服务层测试
└── test_models.py       # 模型测试
```

### 2. 编写测试

```python
import pytest
from fastapi.testclient import TestClient

class TestAPI:
    def test_get_tick_data(self, client: TestClient):
        response = client.get("/api/v1/market/tick/000001?exchange=SZ")
        assert response.status_code == 200
        
        data = response.json()
        assert data["code"] == 200
        assert "data" in data
```

### 3. 运行测试

```bash
# 运行所有测试
pytest

# 运行特定测试
pytest tests/test_api.py::TestAPI::test_get_tick_data

# 生成覆盖率报告
pytest --cov=app --cov-report=html

# 运行性能测试
pytest -m slow
```

## 添加新功能

### 1. 添加新的API端点

1. **定义数据模型** (`app/models/`):

```python
# request.py
class NewRequest(BaseModel):
    param1: str
    param2: int

# response.py  
class NewResponse(BaseModel):
    result: str
```

2. **实现业务逻辑** (`app/services/`):

```python
class NewService:
    async def process_request(self, request: NewRequest) -> NewResponse:
        # 业务逻辑实现
        return NewResponse(result="success")
```

3. **添加API路由** (`app/api/v1/`):

```python
@router.post("/new-endpoint")
async def new_endpoint(
    request: NewRequest,
    service: NewService = Depends(get_new_service)
) -> ApiResponse[NewResponse]:
    result = await service.process_request(request)
    return ApiResponse.success(data=result)
```

4. **编写测试**:

```python
def test_new_endpoint(client: TestClient):
    response = client.post("/api/v1/new-endpoint", json={
        "param1": "test",
        "param2": 123
    })
    assert response.status_code == 200
```

### 2. 添加新的数据源

1. **创建数据源接口**:

```python
from abc import ABC, abstractmethod

class DataSource(ABC):
    @abstractmethod
    async def get_tick_data(self, symbol: str) -> TickData:
        pass
```

2. **实现具体数据源**:

```python
class RealDataSource(DataSource):
    async def get_tick_data(self, symbol: str) -> TickData:
        # 连接真实数据源
        pass
```

3. **配置依赖注入**:

```python
def get_data_source() -> DataSource:
    source_type = get_settings().DATA_SOURCE_TYPE
    if source_type == "real":
        return RealDataSource()
    else:
        return MockDataSource()
```

## 配置管理

### 1. 环境配置

支持三种环境：
- `development`: 开发环境
- `testing`: 测试环境  
- `production`: 生产环境

### 2. 配置优先级

1. 环境变量
2. `.env` 文件
3. `config.yaml` 文件
4. 默认值

### 3. 添加新配置

1. **在 Settings 类中添加字段**:

```python
class Settings(BaseSettings):
    NEW_CONFIG: str = Field(default="default_value", description="新配置项")
```

2. **在 config.yaml 中添加配置**:

```yaml
development:
  new_config: "dev_value"
  
production:
  new_config: "prod_value"
```

## 日志和监控

### 1. 日志记录

```python
from loguru import logger

# 基础日志
logger.info("信息日志")
logger.warning("警告日志")
logger.error("错误日志")

# 结构化日志
logger.bind(user_id=123).info("用户操作日志")

# API日志
from app.core.logging import get_api_logger
api_logger = get_api_logger()
api_logger.info("API请求日志")
```

### 2. 性能监控

```python
import time
from loguru import logger

async def timed_operation():
    start_time = time.time()
    try:
        # 执行操作
        result = await some_operation()
        return result
    finally:
        duration = time.time() - start_time
        logger.info(f"操作耗时: {duration:.3f}秒")
```

## 部署和发布

### 1. 版本管理

使用语义化版本号：
- `MAJOR.MINOR.PATCH`
- 如：`1.0.0`, `1.1.0`, `1.1.1`

### 2. 发布流程

1. 更新版本号
2. 运行完整测试
3. 生成变更日志
4. 创建发布标签
5. 部署到生产环境

### 3. 环境变量检查

部署前检查必要的环境变量：

```bash
# 生产环境必需的环境变量
export APP_ENV=production
export DEBUG=false
export HOST=0.0.0.0
export PORT=8000
export LOG_LEVEL=INFO
```

## 故障排查

### 1. 常见问题

- **服务启动失败**: 检查端口占用、依赖安装
- **API响应慢**: 检查数据源连接、缓存配置
- **内存泄漏**: 检查异步任务、连接池配置

### 2. 调试技巧

```python
# 启用调试模式
import logging
logging.basicConfig(level=logging.DEBUG)

# 使用断点调试
import pdb; pdb.set_trace()

# 性能分析
import cProfile
cProfile.run('your_function()')
```

### 3. 日志分析

```bash
# 查看错误日志
tail -f logs/error.log

# 分析API访问日志
grep "POST" logs/api.log | wc -l

# 查看特定时间段的日志
grep "2024-01-01" logs/app.log
```
