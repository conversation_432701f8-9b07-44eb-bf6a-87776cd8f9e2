"""
简化的API响应模型定义
"""

from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


class ApiResponse(BaseModel):
    """统一API响应格式"""
    
    code: int = Field(description="状态码", example=200)
    message: str = Field(description="响应消息", example="成功")
    data: Optional[Any] = Field(default=None, description="响应数据")
    timestamp: datetime = Field(
        default_factory=datetime.now,
        description="响应时间戳"
    )
    
    @classmethod
    def success(cls, data: Any = None, message: str = "成功") -> "ApiResponse":
        """创建成功响应"""
        return cls(code=200, message=message, data=data)
    
    @classmethod
    def error(cls, code: int = 500, message: str = "服务器内部错误", data: Any = None) -> "ApiResponse":
        """创建错误响应"""
        return cls(code=code, message=message, data=data)


class TickDataResponse(BaseModel):
    """股票成交信息响应数据 - 简化版"""
    
    symbol: str = Field(description="股票代码")
    exchange: str = Field(description="交易所代码")
    name: str = Field(default="", description="股票名称")
    datetime: datetime = Field(description="数据时间")
    timestamp: int = Field(description="时间戳")
    
    # 基础价格信息
    last_price: float = Field(default=0.0, description="最新价")
    open: float = Field(default=0.0, description="开盘价")
    high: float = Field(default=0.0, description="最高价")
    low: float = Field(default=0.0, description="最低价")
    pre_close: float = Field(default=0.0, description="昨收价")
    
    # 成交信息
    volume: float = Field(default=0.0, description="成交量")
    amount: float = Field(default=0.0, description="成交额")
    
    # 计算字段
    change: float = Field(default=0.0, description="涨跌额")
    change_percent: float = Field(default=0.0, description="涨跌幅(%)")


class KlineDataResponse(BaseModel):
    """K线数据响应 - 简化版"""
    
    symbol: str = Field(description="股票代码")
    exchange: str = Field(description="交易所代码")
    datetime: datetime = Field(description="K线时间")
    timestamp: int = Field(description="时间戳")
    interval: str = Field(description="K线周期")
    
    open: float = Field(default=0.0, description="开盘价")
    high: float = Field(default=0.0, description="最高价")
    low: float = Field(default=0.0, description="最低价")
    close: float = Field(default=0.0, description="收盘价")
    pre_close: float = Field(default=0.0, description="昨收价")
    volume: float = Field(default=0.0, description="成交量")
    amount: float = Field(default=0.0, description="成交额")
    
    # 计算字段
    change: float = Field(default=0.0, description="涨跌额")
    change_percent: float = Field(default=0.0, description="涨跌幅(%)")


class MultiTickDataResponse(BaseModel):
    """多个股票成交信息响应"""
    
    total: int = Field(description="总数量")
    data: List[TickDataResponse] = Field(description="股票成交信息列表")


class MultiKlineDataResponse(BaseModel):
    """多个股票K线信息响应"""
    
    total: int = Field(description="总数量")
    data: List[Dict[str, Any]] = Field(description="股票K线信息列表")
