"""
测试配置和夹具
"""

import asyncio
import os
import pytest
from fastapi.testclient import TestClient
from httpx import AsyncClient

# 设置测试环境
os.environ["APP_ENV"] = "testing"
os.environ["DEBUG"] = "true"
os.environ["TICK_SUBSCRIPTION_ENABLED"] = "false"

from main import app


@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def client():
    """创建测试客户端"""
    with TestClient(app) as c:
        yield c


@pytest.fixture
async def async_client():
    """创建异步测试客户端"""
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac


@pytest.fixture
def sample_tick_data():
    """示例tick数据"""
    return {
        "symbol": "000001",
        "exchange": "SZ",
        "name": "平安银行",
        "last_price": 10.50,
        "open": 10.30,
        "high": 10.60,
        "low": 10.20,
        "pre_close": 10.40,
        "volume": 1000000,
        "amount": 10500000,
    }


@pytest.fixture
def sample_kline_data():
    """示例K线数据"""
    return [
        {
            "symbol": "000001",
            "exchange": "SZ",
            "open": 10.30,
            "high": 10.60,
            "low": 10.20,
            "close": 10.50,
            "volume": 1000000,
            "amount": 10500000,
        }
    ]


@pytest.fixture
def sample_symbols():
    """示例股票代码列表"""
    return ["000001", "000002", "600000"]


@pytest.fixture
def sample_exchanges():
    """示例交易所代码列表"""
    return ["SZ", "SZ", "SH"]
