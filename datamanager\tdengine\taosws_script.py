"""
TDengine脚本, 用于在TDengine中创建数据库和数据表。
"""
# # 创建数据库
# CREATE_DATABASE_SCRIPT = """
# CREATE DATABASE IF NOT EXISTS {} KEEP 36500
# """
DB_NAME = "real_data"
# 创建bar超级表
BAR_TABLE = {
    "keys": {
        "datetime": "TIMESTAMP",
        "open": "DOUBLE",
        "high": "DOUBLE",
        "low": "DOUBLE",
        "close": "DOUBLE",
        "pre_close": "DOUBLE",
        "volume": "DOUBLE",
        "amount": "DOUBLE",
    },
    "tags": {
        "vt_symbol": "BINARY(20)",
        "symbol": "BINARY(20)",
        "exchange": "BINARY(10)",
    }
}
# 
"""
CREATE TOPIC tick AS SELECT vt_symbol, datetime, last_price, high, low, pre_close, volume, amount FROM real_data.tick;

"""
# 创建tick超级表 net_volume
TICK_TABLE = {
    "keys": {
        "datetime": "TIMESTAMP",
        "last_price": "DOUBLE",
        "open": "DOUBLE",
        "high": "DOUBLE",
        "low": "DOUBLE",
        "pre_close": "DOUBLE",
        "volume": "DOUBLE",
        "amount": "DOUBLE",
        "net_volume": "DOUBLE",
        "net_amount": "DOUBLE",
        "direction": "DOUBLE",
        "bid_price_1": "DOUBLE",
        "bid_price_2": "DOUBLE",
        "bid_price_3": "DOUBLE",
        "bid_price_4": "DOUBLE",
        "bid_price_5": "DOUBLE",
        "ask_price_1": "DOUBLE",
        "ask_price_2": "DOUBLE",
        "ask_price_3": "DOUBLE",
        "ask_price_4": "DOUBLE",
        "ask_price_5": "DOUBLE",
        "bid_volume_1": "DOUBLE",
        "bid_volume_2": "DOUBLE",
        "bid_volume_3": "DOUBLE",
        "bid_volume_4": "DOUBLE",
        "bid_volume_5": "DOUBLE",
        "ask_volume_1": "DOUBLE",
        "ask_volume_2": "DOUBLE",
        "ask_volume_3": "DOUBLE",
        "ask_volume_4": "DOUBLE",
        "ask_volume_5": "DOUBLE",
    },
    "tags": {
        "vt_symbol": "BINARY(20)",
        "symbol": "BINARY(20)",
        "exchange": "BINARY(10)",
    }
}

