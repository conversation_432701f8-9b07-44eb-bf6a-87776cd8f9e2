#coding=utf-8


"""
常量定义模块
"""


"""
账号类型
"""
# 期货
FUTURE_ACCOUNT = 1
# 股票
SECURITY_ACCOUNT = 2
# 信用
CREDIT_ACCOUNT = 3
# 期货期权
FUTURE_OPTION_ACCOUNT = 5
# 股票期权
STOCK_OPTION_ACCOUNT = 6
# 沪港通
HUGANGTONG_ACCOUNT = 7
# 美股收益互换
INCOME_SWAP_ACCOUNT = 8
# 全国股转账号
NEW3BOARD_ACCOUNT = 10
# 深港通
SHENGANGTONG_ACCOUNT = 11
# 场外理财账户
AT_OFFSITEBANKING = 13
# 期货外盘
AT_OUTTER_FUTURE = 1001
# IB
AT_IB = 1002
# 场外托管
AT_NS_TRUSTBANK = 15001
# 银行间账号
AT_INTERBANK = 15002
# 银行账号
AT_BANK = 15003
# 场外账号
AT_OTC = 15005

ACCOUNT_TYPE_DICT = {
    FUTURE_ACCOUNT : 'FUTURE',
    SECURITY_ACCOUNT : 'STOCK',
    CREDIT_ACCOUNT : 'CREDIT',
    FUTURE_OPTION_ACCOUNT : 'FUTURE_OPTION',
    STOCK_OPTION_ACCOUNT : 'STOCK_OPTION',
    HUGANGTONG_ACCOUNT : 'HUGANGTONG',
    SHENGANGTONG_ACCOUNT : 'SHENGANGTONG',
    NEW3BOARD_ACCOUNT : 'NEW3BOARD',
    INCOME_SWAP_ACCOUNT: 'INCOME_SWAP',
    AT_OFFSITEBANKING : 'OFFSITEBANKING',
    AT_OUTTER_FUTURE : 'OUTTER_FUTURE',
    AT_IB : 'IB',
    AT_NS_TRUSTBANK : 'NS_TRUSTBANK',
    AT_INTERBANK : 'INTERBANK',
    AT_BANK : 'BANK',
    AT_OTC : 'OTC',
}


"""
委托类型
"""
#/ *期货六键风格 * /
FUTURE_OPEN_LONG = 0 # 开多
FUTURE_CLOSE_LONG_HISTORY = 1 # 平昨多
FUTURE_CLOSE_LONG_TODAY = 2 # 平今多
FUTURE_OPEN_SHORT = 3 # 开空
FUTURE_CLOSE_SHORT_HISTORY = 4 # 平昨空
FUTURE_CLOSE_SHORT_TODAY = 5 # 平今空
# / *期货四键风格 * /
FUTURE_CLOSE_LONG_TODAY_FIRST = 6 # 平多，优先平今
FUTURE_CLOSE_LONG_HISTORY_FIRST = 7 # 平多，优先平昨
FUTURE_CLOSE_SHORT_TODAY_FIRST = 8 # 平空，优先平今
FUTURE_CLOSE_SHORT_HISTORY_FIRST = 9 # 平空，优先平昨

# / *期货两键风格 * /
FUTURE_CLOSE_LONG_TODAY_HISTORY_THEN_OPEN_SHORT = 10 # 卖出，如有多仓，优先平仓，优先平今，如有余量，再开空
FUTURE_CLOSE_LONG_HISTORY_TODAY_THEN_OPEN_SHORT = 11 # 卖出，如有多仓，优先平仓，优先平昨，如有余量，再开空
FUTURE_CLOSE_SHORT_TODAY_HISTORY_THEN_OPEN_LONG = 12 # 买入，如有空仓，优先平仓，优先平今，如有余量，再开多
FUTURE_CLOSE_SHORT_HISTORY_TODAY_THEN_OPEN_LONG = 13 # 买入，如有空仓，优先平仓，优先平昨，如有余量，再开多
FUTURE_OPEN = 14 # 买入，不优先平仓
FUTURE_CLOSE = 15 # 卖出，不优先平仓

# / *期货 - 跨商品套利 * /
FUTURE_ARBITRAGE_OPEN = 16 # 开仓
FUTURE_ARBITRAGE_CLOSE_HISTORY_FIRST = 17 # 平, 优先平昨
FUTURE_ARBITRAGE_CLOSE_TODAY_FIRST = 18 # 平, 优先平今

# / *期货展期 * /
FUTURE_RENEW_LONG_CLOSE_HISTORY_FIRST = 19 # 看多, 优先平昨
FUTURE_RENEW_LONG_CLOSE_TODAY_FIRST = 20 # 看多，优先平今
FUTURE_RENEW_SHORT_CLOSE_HISTORY_FIRST = 21 # 看空，优先平昨
FUTURE_RENEW_SHORT_CLOSE_TODAY_FIRST = 22 # 看空，优先平今

# / *股票期权 * /
STOCK_OPTION_BUY_OPEN = 48 # 买入开仓，以下用于个股期权交易业务
STOCK_OPTION_SELL_CLOSE = 49 # 卖出平仓
STOCK_OPTION_SELL_OPEN = 50 # 卖出开仓
STOCK_OPTION_BUY_CLOSE = 51 # 买入平仓
STOCK_OPTION_COVERED_OPEN = 52 # 备兑开仓
STOCK_OPTION_COVERED_CLOSE = 53 # 备兑平仓
STOCK_OPTION_CALL_EXERCISE = 54 # 认购行权
STOCK_OPTION_PUT_EXERCISE = 55 # 认沽行权
STOCK_OPTION_SECU_LOCK = 56 # 证券锁定
STOCK_OPTION_SECU_UNLOCK = 57 # 证券解锁

# /*期货期权*/
OPTION_FUTURE_OPTION_EXERCISE = 100 # 期货期权行权

# /*组合期货*/
COMPOSE_OPEN_LONG                   = 110 # 组合开多
COMPOSE_OPEN_SHORT                  = 111 # 组合开空
COMPOSE_CLOSE_LONG_TODAY_FIRST      = 112 # 组合平多-优先平今
COMPOSE_CLOSE_LONG_HISTORY_FIRST    = 113 # 组合平多-优先平昨
COMPOSE_CLOSE_SHORT_TODAY_FIRST     = 114 # 组合平空-优先平今
COMPOSE_CLOSE_SHORT_HISTORY_FIRST   = 115 # 组合平空-优先平昨
COMPOSE_ONEKEY_FUTURE_TODAY_FIRST   = 116 # 一键期货-优先平今
COMPOSE_ONEKEY_FUTURE_HISTORY_FIRST = 117 # 一键期货-优先平昨
COMPOSE_FUTURE_ADJUST_TODAY_FIRST   = 118 # 组合调仓-优先平今
COMPOSE_FUTURE_ADJUST_HISTORY_FIRST = 119 # 组合调仓-优先平昨

# / *组合期权 * /
COMPOSE_OPTION_COMB_EXERCISE = 137 # 组合行权
COMPOSE_OPTION_BUILD_COMB_STRATEGY = 138 # 构建组合策略
COMPOSE_OPTION_RELEASE_COMB_STRATEGY = 139 # 解除组合策略

# /*期货套利*/
FUTURE_HEDGE = 400 # 期货套利

# /*ETF申赎*/
ETF_PURCHASE = 134 # 申购
ETF_REDEMPTION = 135 # 赎回

STOCK_BUY = 23
STOCK_SELL = 24
CREDIT_BUY = 23    #担保品买入
CREDIT_SELL = 24   #担保品卖出
CREDIT_FIN_BUY = 27 #融资买入
CREDIT_SLO_SELL  = 28 #融券卖出
CREDIT_BUY_SECU_REPAY = 29 #买券还券
CREDIT_DIRECT_SECU_REPAY = 30 #直接还券
CREDIT_SELL_SECU_REPAY  = 31 #卖券还款
CREDIT_DIRECT_CASH_REPAY = 32 #直接还款
CREDIT_FIN_BUY_SPECIAL = 40 #专项融资买入
CREDIT_SLO_SELL_SPECIAL  = 41 #专项融券卖出
CREDIT_BUY_SECU_REPAY_SPECIAL = 42 #专项买券还券
CREDIT_DIRECT_SECU_REPAY_SPECIAL = 43 #专项直接还券
CREDIT_SELL_SECU_REPAY_SPECIAL  = 44 #专项卖券还款
CREDIT_DIRECT_CASH_REPAY_SPECIAL = 45 #专项直接还款

ORDER_TYPE_SET = {
    STOCK_BUY
    , STOCK_SELL
    , CREDIT_BUY
    , CREDIT_SELL
    , CREDIT_FIN_BUY
    , CREDIT_SLO_SELL
    , CREDIT_BUY_SECU_REPAY
    , CREDIT_DIRECT_SECU_REPAY
    , CREDIT_SELL_SECU_REPAY
    , CREDIT_DIRECT_CASH_REPAY
    , CREDIT_FIN_BUY_SPECIAL
    , CREDIT_SLO_SELL_SPECIAL
    , CREDIT_BUY_SECU_REPAY_SPECIAL
    , CREDIT_DIRECT_SECU_REPAY_SPECIAL
    , CREDIT_SELL_SECU_REPAY_SPECIAL
    , CREDIT_DIRECT_CASH_REPAY_SPECIAL
}

"""
报价类型
"""
# 最新价
LATEST_PRICE = 5
# 指定价/限价
FIX_PRICE = 11
# 市价最优价[郑商所][期货]
MARKET_BEST = 18
# 市价即成剩撤[大商所][期货]
MARKET_CANCEL = 19
# 市价全额成交或撤[大商所][期货]
MARKET_CANCEL_ALL = 20
# 市价最优一档即成剩撤[中金所][期货]
MARKET_CANCEL_1 = 21
# 市价最优五档即成剩撤[中金所][期货]
MARKET_CANCEL_5 = 22
# 市价最优一档即成剩转[中金所][期货]
MARKET_CONVERT_1 = 23
# 市价最优五档即成剩转[中金所][期货]
MARKET_CONVERT_5 = 24
# 最优五档即时成交剩余撤销[上交所][北交所][股票]
MARKET_SH_CONVERT_5_CANCEL = 42
# 最优五档即时成交剩转限价[上交所][北交所][股票]
MARKET_SH_CONVERT_5_LIMIT = 43
# 对手方最优价格委托[上交所[股票]][深交所[股票][期权]][北交所[股票]]
MARKET_PEER_PRICE_FIRST = 44
# 本方最优价格委托[上交所[股票]][深交所[股票][期权]][北交所[股票]]
MARKET_MINE_PRICE_FIRST = 45
# 即时成交剩余撤销委托[深交所][股票][期权]
MARKET_SZ_INSTBUSI_RESTCANCEL = 46
# 最优五档即时成交剩余撤销[深交所][股票][期权]
MARKET_SZ_CONVERT_5_CANCEL = 47
# 全额成交或撤销委托[深交所][股票][期权]
MARKET_SZ_FULL_OR_CANCEL = 48


"""
市场类型
"""
# 上海市场
SH_MARKET = 0
# 深圳市场
SZ_MARKET = 1
# 北交所
MARKET_ENUM_BEIJING = 70
# 上期所
MARKET_ENUM_SHANGHAI_FUTURE = 3
# 大商所
MARKET_ENUM_DALIANG_FUTURE = 4
# 郑商所
MARKET_ENUM_ZHENGZHOU_FUTURE = 5
# 中金所
MARKET_ENUM_INDEX_FUTURE = 2
# 能源中心
MARKET_ENUM_INTL_ENERGY_FUTURE = 6
# 广期所
MARKET_ENUM_GUANGZHOU_FUTURE = 75
# 上海期权
MARKET_ENUM_SHANGHAI_STOCK_OPTION = 7
# 深圳期权
MARKET_ENUM_SHENZHEN_STOCK_OPTION = 67

"""
市场类型-字符串
"""
# 上交所
MARKET_SHANGHAI = 'SH'
# 深交所
MARKET_SHENZHEN = 'SZ'
# 北交所
MARKET_BEIJING = 'BJ'
# 上期所
MARKET_SHANGHAI_FUTURE = 'SF'
# 大商所
MARKET_DALIANG_FUTURE = 'DF'
# 郑商所
MARKET_ZHENGZHOU_FUTURE = 'ZF'
# 中金所
MARKET_INDEX_FUTURE = 'IF'
# 能源中心
MARKET_INTL_ENERGY_FUTURE = 'INE'
# 广期所
MARKET_GUANGZHOU_FUTURE = 'GF'
# 上海期权
MARKET_SHANGHAI_STOCK_OPTION = 'SHO'
# 深圳期权
MARKET_SHENZHEN_STOCK_OPTION = 'SZO'

MARKET_STR_TO_ENUM_MAPPING = {
    MARKET_SHANGHAI : SH_MARKET,
    MARKET_SHENZHEN : SZ_MARKET,
    MARKET_BEIJING : MARKET_ENUM_BEIJING,
    MARKET_SHANGHAI_FUTURE : MARKET_ENUM_SHANGHAI_FUTURE,
    MARKET_DALIANG_FUTURE : MARKET_ENUM_DALIANG_FUTURE,
    MARKET_ZHENGZHOU_FUTURE : MARKET_ENUM_ZHENGZHOU_FUTURE,
    MARKET_INDEX_FUTURE : MARKET_ENUM_INDEX_FUTURE,
    MARKET_INTL_ENERGY_FUTURE: MARKET_ENUM_INTL_ENERGY_FUTURE,
    MARKET_GUANGZHOU_FUTURE : MARKET_ENUM_GUANGZHOU_FUTURE,
    MARKET_SHANGHAI_STOCK_OPTION : MARKET_ENUM_SHANGHAI_STOCK_OPTION,
    MARKET_SHENZHEN_STOCK_OPTION : MARKET_ENUM_SHENZHEN_STOCK_OPTION,
}


"""
委托状态
"""
# 未报
ORDER_UNREPORTED = 48
# 待报
ORDER_WAIT_REPORTING = 49
# 已报
ORDER_REPORTED = 50
# 已报待撤
ORDER_REPORTED_CANCEL = 51
# 部成待撤
ORDER_PARTSUCC_CANCEL = 52
# 部撤
ORDER_PART_CANCEL = 53
# 已撤
ORDER_CANCELED = 54
# 部成
ORDER_PART_SUCC = 55
# 已成
ORDER_SUCCEEDED = 56
# 废单
ORDER_JUNK = 57
# 未知
ORDER_UNKNOWN = 255


"""
账号状态
"""
#无效
ACCOUNT_STATUS_INVALID = -1
#正常
ACCOUNT_STATUS_OK = 0
#连接中
ACCOUNT_STATUS_WAITING_LOGIN = 1
#登陆中
ACCOUNT_STATUSING = 2
#失败
ACCOUNT_STATUS_FAIL = 3
#初始化中
ACCOUNT_STATUS_INITING = 4
#数据刷新校正中
ACCOUNT_STATUS_CORRECTING = 5
#收盘后
ACCOUNT_STATUS_CLOSED = 6
#穿透副链接断开 
ACCOUNT_STATUS_ASSIS_FAIL = 7
#系统停用（总线使用-密码错误超限）
ACCOUNT_STATUS_DISABLEBYSYS = 8
#用户停用（总线使用）
ACCOUNT_STATUS_DISABLEBYUSER = 9

"""
指令交易类型
"""
#无效值
TDT_INVALID = 0x00000000
#股票交易
TDT_STOCK = 0x00000001
#期货交易
TDT_FUTURE = 0x00000002
#组合交易
TDT_COMPOSE = 0x00000004
#信用交易
TDT_CREDIT = 0x00000008
#黄金T+D交易
TDT_GOLD = 0x00000010
#股票期权
TDT_STK_OPTION = 0x00000020
#外盘
TDT_OUTTER = 0x00000040
#沪港通
TDT_HUGANGTONG = 0x00000080
#全国股转
TDT_NEW3BOARD = 0x00000100
#场外业务
TDT_NON_STANDARD = 0x00000200
#期货期权
TDT_FUTURE_OPTION = 0x00000400
#电子盘交易
TDT_ELECTRONIC = 0x00000800
#ETF申赎
TDT_ETF = 0x00001000
#全业务
TDT_PB_FULL = 0x00002000
#深港通
TDT_SHENGANGTONG = 0x00004000
#银行间业务
TDT_INTERBANK = 0x00008000
#风控员界面
TDT_RISK = 0x00010000
#日内回转界面
TDT_PF_REVOLVING = 0x00020000
#数字货币
TDT_DIGICOIN = 0x00040000
#银行账号
TDT_BANK = 0x00040001
#投组管理
TDT_PORTFOLIO = 0x00080000
#固收业务
TDT_FICC = 0x00100000
#券商理财
TDT_BROKER_FINANCING = 0x00200000
#转融通
TDT_LMT = 0x00400000
#收益互换
TDT_INCOME_SWAP = 0x00800000
#快捷交易
TDT_FAST_TRADE = 0x01000000
#转托管
TDT_TOC = 0x02000000
#利率互换
TDT_IRS = 0x04000000
#债券投标
TDT_BTD = 0x08000000
#网下新股
TDT_OFF_IPO = 0x10000000
#融券通
TDT_SECLENDING = 0x20000000
#转账录入
TDT_TRANSFER = 0x40000000

#超int上限
#这里的数值是相关的业务全部求和的 例如TDT_NOT_COMPOSE = 上面全部相加 减去TDT_COMPOSE TDT_NORMALTRADE是上面所有的去掉TDT_NON_STANDARD以及TDT_INTERBANK
#不包含组合交易的其他交易类型
TDT_NOT_COMPOSE = 0x20F0DFFB
#普通交易类型，不包含非标业务和银行间业务
TDT_NORMALTRADE = 0x3FF07DFF

#占位
#所有交易界面
TDT_TRADE = 0x7FF4FFFF
#所有界面
TDT_ALL = 0x3FF3FFFF
#综合交易界面
TDT_INTEGRATED = 0x000C5DFF


"""
指令状态
"""
#风控检查中
OCS_CHECKING = -1
#审批中
OCS_APPROVING = 0
#已驳回
OCS_REJECTED = 1
#运行中
OCS_RUNNING = 2
#撤销中
OCS_CANCELING_DEPRECATED = 3
#已完成
OCS_FINISHED = 4
#已撤销
OCS_STOPPED = 5
#强制撤销
OCS_FORCE_COMPLETED_DEPRECATED = 6
#风控驳回
OCS_CHECKFAILED = 7
#撤销审批中
OCS_CANCELING_APPROVING = 8
#撤销驳回
OCS_CANCELING_REJECTED = 9
#预埋指令
OCS_PRESETTING = 10
#撤销指令至撤销人
OCS_CANCEL_TO_USER = 11
#驳回指令至上一级
OCS_CANCEL_REJECT_TO_UPPER_LEVEL = 12
#补单
OCS_PATCH_RUNNNING = 13
#暂停指令并暂停任务
OCS_PAUSE_PAUSEORDER = 14
#暂停指令并撤销任务
OCS_PAUSE_CANCELORDER = 15
#场外：转账确认
OCS_NS_TRANSFERCONFIRM = 101
#场外：已结算
OCS_NS_SETTLEUP = 102
#场外：撤销中
OCS_NS_CANCELING = 103
#场外：认购确认中
OCS_OTC_SUBSCRIBE_CONFIRMING = 104
#场外：TA确认中
OCS_OTC_TA_CONFIRMING = 105
#场外：基金成立中
OCS_OTC_FUND_ESTABLISHING = 106
#场外：交收确认中
OCS_OTC_SETTLE_CONFIRMING = 107
#场外：询价成交确认中
OCS_OTC_QUERY_CONFIRMING = 108
#场外：存款成交确认中
OCS_OTC_DEPOSIT_CONFIRMING = 109
#场外：支取成交确认中
OCS_OTC_DRAW_CONFIRMING = 110
#已完成
OCS_INTERBANK_DEAL_FINISHED = 201
#已结束
OCS_INTERBANK_SETTLE_FINISHED = 202
#置为完成
OCS_INTERBANK_MANAUL_FINISHED = 203
#锁定中
OCS_INTERBANK_LOCK = 204
#任务创建运行
OCS_TASK_STATUS_RUNNING = 1001
#任务被撤销了
OCS_TASK_STATUS_CANCELED = 1002
#任务完成
OCS_TASK_STATUS_FINISHED = 1003
#任务被暂停了
OCS_TASK_STATUS_PAUSED = 1004
#指令回溯
OCS_INTERBANK_CMD_ROLLBACK = 1100
#指令解锁
OCS_INTERBANK_CMD_UNLOCK = 1101
#目前只作留痕用
OCS_CANCELING_ORDER = 1300
#改价中
OCS_CHANGE_PRICE_ING = 1400
#改价成功
OCS_CHANGE_PRICE_SUCCESS = 1401
#改价失败
OCS_CHANGE_PRICE_FAIL = 1402
#改触价
OCS_CHANGE_PRICE_TRIGGER_PRICE = 1403
#算法指令改单中
OCS_CHANGE_COMMAND = 1404
#普通指令准备改单中
OCS_INORDER_TO_CHANGE_COMMAND_NORMAL = 1405
#普通指令改单中
OCS_CHANGE_COMMAND_NORMAL = 1406
#风控员确认中
OCS_RISKCONTROLER_APPROVING = 1407
#无效值
OCS_CMD_INVALID = -2

"""
指令操作类型
"""
#未知
OPT_INVALID = -1
#开多
OPT_OPEN_LONG = 0
#平昨多,平多
OPT_CLOSE_LONG_HISTORY = 1
#平今多
OPT_CLOSE_LONG_TODAY = 2
#开空
OPT_OPEN_SHORT = 3
#平昨空,平空
OPT_CLOSE_SHORT_HISTORY = 4
#平今空
OPT_CLOSE_SHORT_TODAY = 5
#平多优先平今
OPT_CLOSE_LONG_TODAY_FIRST = 6
#平多优先平昨
OPT_CLOSE_LONG_HISTORY_FIRST = 7
#平空优先平今
OPT_CLOSE_SHORT_TODAY_FIRST = 8
#平空优先平昨
OPT_CLOSE_SHORT_HISTORY_FIRST = 9
#卖出优先平今
OPT_CLOSE_LONG_TODAY_HISTORY_THEN_OPEN_SHORT = 10
#卖出优先平昨
OPT_CLOSE_LONG_HISTORY_TODAY_THEN_OPEN_SHORT = 11
#买入优先平今
OPT_CLOSE_SHORT_TODAY_HISTORY_THEN_OPEN_LONG = 12
#买入优先平昨
OPT_CLOSE_SHORT_HISTORY_TODAY_THEN_OPEN_LONG = 13
#平多
OPT_CLOSE_LONG = 14
#平空
OPT_CLOSE_SHORT = 15
#买入,开仓,买入
OPT_OPEN = 16
#卖出,平仓,卖出
OPT_CLOSE = 17
#买入
OPT_BUY = 18
#卖出
OPT_SELL = 19
#融资买入
OPT_FIN_BUY = 20
#融券卖出
OPT_SLO_SELL = 21
#买券还券
OPT_BUY_SECU_REPAY = 22
#直接还券
OPT_DIRECT_SECU_REPAY = 23
#卖券还款
OPT_SELL_CASH_REPAY = 24
#直接还款
OPT_DIRECT_CASH_REPAY = 25
#基金申购
OPT_FUND_SUBSCRIBE = 26
#基金赎回
OPT_FUND_REDEMPTION = 27
#基金合并
OPT_FUND_MERGE = 28
#基金分拆
OPT_FUND_SPLIT = 29
#质押入库
OPT_PLEDGE_IN = 30
#质押出库
OPT_PLEDGE_OUT = 31
#买入开仓
OPT_OPTION_BUY_OPEN = 32
#卖出平仓
OPT_OPTION_SELL_CLOSE = 33
#卖出开仓
OPT_OPTION_SELL_OPEN = 34
#买入平仓
OPT_OPTION_BUY_CLOSE = 35
#备兑开仓
OPT_OPTION_COVERED_OPEN = 36
#备兑平仓
OPT_OPTION_COVERED_CLOSE = 37
#认购行权
OPT_OPTION_CALL_EXERCISE = 38
#认沽行权
OPT_OPTION_PUT_EXERCISE = 39
#证券锁定
OPT_OPTION_SECU_LOCK = 40
#证券解锁
OPT_OPTION_SECU_UNLOCK = 41
#定价买入
OPT_N3B_PRICE_BUY = 42
#定价卖出
OPT_N3B_PRICE_SELL = 43
#成交确认买入
OPT_N3B_CONFIRM_BUY = 44
#成交确认卖出
OPT_N3B_CONFIRM_SELL = 45
#互报成交确认买入
OPT_N3B_REPORT_CONFIRM_BUY = 46
#互报成交确认卖出
OPT_N3B_REPORT_CONFIRM_SELL = 47
#限价买入
OPT_N3B_LIMIT_PRICE_BUY = 48
#限价卖出
OPT_N3B_LIMIT_PRICE_SELL = 49
#期货期权行权
OPT_FUTURE_OPTION_EXERCISE = 50
#可转债转股
OPT_CONVERT_BONDS = 51
#可转债回售
OPT_SELL_BACK_BONDS = 52
#股票配股
OPT_STK_ALLOTMENT = 53
#股票增发
OPT_STK_INCREASE_SHARE = 54
#担保品划入
OPT_COLLATERAL_TRANSFER_IN = 55
#担保品划出
OPT_COLLATERAL_TRANSFER_OUT = 56
#意向申报买入
OPT_BLOCK_INTENTION_BUY = 57
#意向申报卖出
OPT_BLOCK_INTENTION_SELL = 58
#定价申报买入
OPT_BLOCK_PRICE_BUY = 59
#定价申报卖出
OPT_BLOCK_PRICE_SELL = 60
#成交申报买入
OPT_BLOCK_CONFIRM_BUY = 61
#成交申报卖出
OPT_BLOCK_CONFIRM_SELL = 62
#盘后定价买入
OPT_BLOCK_CLOSE_PRICE_BUY = 63
#盘后定价卖出
OPT_BLOCK_CLOSE_PRICE_SELL = 64
#黄金交割买
OPT_GOLD_PRICE_DELIVERY_BUY = 65
#黄金交割卖
OPT_GOLD_PRICE_DELIVERY_SELL = 66
#黄金中立仓买
OPT_GOLD_PRICE_MIDDLE_BUY = 67
#黄金中立仓卖
OPT_GOLD_PRICE_MIDDLE_SELL = 68
#组合交易一键买卖
OPT_COMPOSE_ONEKEY_BUYSELL = 69
#组合交易港股通买入
OPT_COMPOSE_GGT_BUY = 70
#组合交易港股通卖出
OPT_COMPOSE_GGT_SELL = 71
#零股卖出
OPT_ODD_SELL = 72
#成份股买入
OPT_ETF_STOCK_BUY = 73
#成份股卖出
OPT_ETF_STOCK_SELL = 74
#
OPT_OTC_FUND_BEGIN = 200
#认购
OPT_OTC_FUND_SUBSCRIBE = OPT_OTC_FUND_BEGIN
#申购
OPT_OTC_FUND_PURCHASE = 201
#赎回
OPT_OTC_FUND_REDEMPTION = 202
#转换
OPT_OTC_FUND_CONVERT = 203
#分红方式变更
OPT_OTC_FUND_BONUS_TYPE_UPDATE = 204
#协议存款
OPT_OTC_CONTRACTUAL_DEPOSIT = 205
#非协议存款
OPT_OTC_NON_CONTRACTUAL_DEPOSIT = 206
#协议存款询价
OPT_OTC_CONTRACTUAL_DEPOSIT_ASK = 207
#非协议存款询价
OPT_OTC_NON_CONTRACTUAL_DEPOSIT_ASK = 208
#场外非协议活期存款
OPT_OTC_NON_CONTRACTUAL_DEPOSIT_CUR = 209
#存单支取
OPT_OTC_DRAW_DEPOSIT = 210
#网下询价
OPT_OTC_STOCK_INQUIRY = 230
#网下申购
OPT_OTC_STOCK_PURCHASE = 231
# 场外操作
OPT_OPTION_NS_BEGIN = 1001
#入金
OPT_OPTION_NS_DEPOSIT = OPT_OPTION_NS_BEGIN
#出金
OPT_OPTION_NS_WITHDRAW = 1002
#互转
OPT_OPTION_NS_INOUT = 1003
#ETF申购
OPT_ETF_PURCHASE = 1004
#ETF赎回
OPT_ETF_REDEMPTION = 1005
#外盘买入
OPT_OUTER_BUY = 1006
#外盘卖出
OPT_OUTER_SELL = 1007
#外盘可平买仓
OPT_OUTER_CAN_CLOSE_BUY = 1008
#外盘可平卖仓
OPT_OUTER_CAN_CLOSE_SELL = 1009
#专项融券卖出
OPT_SLO_SELL_SPECIAL = 1010
#专项买券还券
OPT_BUY_SECU_REPAY_SPECIAL = 1011
#专项直接还券
OPT_DIRECT_SECU_REPAY_SPECIAL = 1012
#限价买入
OPT_NEEQ_O3B_LIMIT_PRICE_BUY = 1013
#限价卖出
OPT_NEEQ_O3B_LIMIT_PRICE_SELL = 1014
#现券买入
OPT_IBANK_BOND_BUY = 1015
#现券卖出
OPT_IBANK_BOND_SELL = 1016
#质押式融资回购
OPT_IBANK_FUND_REPURCHASE = 1017
#质押式融券回购
OPT_IBANK_BOND_REPURCHASE = 1018
#质押式融资购回
OPT_IBANK_BOND_REPAY = 1019
#质押式融券购回
OPT_IBANK_FUND_RETRIEVE = 1020
#融券息费
OPT_INTEREST_FEE = 1021
#专项融资买入
OPT_FIN_BUY_SPECIAL = 1022
#专项卖券还款
OPT_SELL_CASH_REPAY_SPECIAL = 1023
#专项直接还款
OPT_DIRECT_CASH_REPAY_SPECIAL = 1024
#货币基金申购
OPT_FUND_PRICE_BUY = 1025
#货币基金赎回
OPT_FUND_PRICE_SELL = 1026
#集合竞价买入
OPT_N3B_CALL_AUCTION_BUY = 1027
#集合竞价卖出
OPT_N3B_CALL_AUCTION_SELL = 1028
#盘后协议买入
OPT_N3B_AFTER_HOURS_BUY = 1029
#盘后协议卖出
OPT_N3B_AFTER_HOURS_SELL = 1030
#ETF套利
OPT_ETF_HEDGE = 1031
#报价回购买入
OPT_QUOTATION_REPURCHASE_BUY = 1032
#报价回购终止续做
OPT_QUOTATION_REPURCHASE_STOP = 1033
#报价回购提前购回
OPT_QUOTATION_REPURCHASE_BEFORE = 1034
#报价回购购回预约
OPT_QUOTATION_REPURCHASE_RESERVATION = 1035
#报价回购取消预约
OPT_QUOTATION_REPURCHASE_CANCEL = 1036
#成交申报配对买入
OPT_BLOCK_CONFIRM_MATCH_BUY = 1037
#成交申报配对卖出
OPT_BLOCK_CONFIRM_MATCH_SELL = 1038
#期货期权放弃行权
OPT_FUTURE_OPTION_ABANDON = 1039
#一键划转
OPT_ONEKEY_TRANSFER = 1040
#一键划入
OPT_ONEKEY_TRANSFER_IN = 1041
#一键划出
OPT_ONEKEY_TRANSFER_OUT = 1042
#盘后定价买入
OPT_AFTER_FIX_BUY = 1043
#盘后定价卖出
OPT_AFTER_FIX_SELL = 1044
#成交申报正回购
OPT_AGREEMENT_REPURCHASE_TRANSACTION_DEC_FORWARD = 1045
#成交申报逆回购
OPT_AGREEMENT_REPURCHASE_TRANSACTION_DEC_REVERSE = 1046
#到期确认
OPT_AGREEMENT_REPURCHASE_EXPIRE_CONFIRM = 1047
#提前购回正回购
OPT_AGREEMENT_REPURCHASE_ADVANCE_REPURCHASE = 1048
#提前购回逆回购
OPT_AGREEMENT_REPURCHASE_ADVANCE_REVERSE = 1049
#到期续做正回购
OPT_AGREEMENT_REPURCHASE_EXPIRE_RENEW = 1050
#到期续做逆回购
OPT_AGREEMENT_REPURCHASE_EXPIRE_REVERSE = 1051
#现券买入
OPT_TRANSACTION_IN_CASH_BUY = 1052
#现券卖出
OPT_TRANSACTION_IN_CASH_SELL = 1053
#买断式融资回购
OPT_OUTRIGHT_REPO_FUND_REPURCHASE = 1054
#买断式融券回购
OPT_OUTRIGHT_REPO_BOND_REPURCHASE = 1055
#买断式融资购回
OPT_OUTRIGHT_REPO_BOND_REPAY = 1056
#买断式融券购回
OPT_OUTRIGHT_REPO_FUND_RETRIEVE = 1057
#分销买入
OPT_DISTRIBUTION_BUYING = 1058
#固定利率换浮动利率
OPT_FIXRATE_TO_FLOATINGRATE = 1059
#浮动利率换固定利率
OPT_FLOATINGRATE_TO_FIXRATE = 1060
#银行间转出托管
OPT_IBANK_TRANSFER_OUT = 1061
#银行间转入托管
OPT_IBANK_TRANSFER_IN = 1062
#意向申报正回购买入
OPT_AGREEMENT_REPURCHASE_INTENTION_BUY = 1063
#意向申报逆回购卖出
OPT_AGREEMENT_REPURCHASE_INTENTION_SELL = 1064
#协议回购成交申报确认
OPT_AGREEMENT_REPURCHASE_BIZ_APPLY_CONFIRM = 1065
#协议回购成交申报拒绝
OPT_AGREEMENT_REPURCHASE_BIZ_APPLY_REJECT = 1066
#协议回购到期续做申报确认
OPT_AGREEMENT_REPURCHASE_CONTINUE_CONFIRM = 1067
#协议回购到期续做申报拒绝
OPT_AGREEMENT_REPURCHASE_CONTINUE_REJECT = 1068
#协议回购换券申报
OPT_AGREEMENT_REPURCHASE_INTENTION_CHANGE_BONDS = 1069
#协议回购换券申报确认
OPT_AGREEMENT_REPURCHASE_INTENTION_CHANGE_BONDS_CONFIRM = 1070
#协议回购换券申报拒绝
OPT_AGREEMENT_REPURCHASE_INTENTION_CHANGE_BONDS_REJECT = 1071
#协议回购正回购提前终止申报确认
OPT_AGREEMENT_REPURCHASE_STOP_AHEAD_CONFIRM = 1072
#协议回购正回购提前终止申报拒绝
OPT_AGREEMENT_REPURCHASE_STOP_AHEAD_REJECT = 1073
#协议回购正回购方解除质押申报
OPT_AGREEMENT_REPURCHASE_RELEASE_PLEDGE = 1074
#协议回购正回购解除质押申报确认
OPT_AGREEMENT_REPURCHASE_RELEASE_PLEDGE_CONFIRM = 1075
#协议回购正回购解除质押申报拒绝
OPT_AGREEMENT_REPURCHASE_RELEASE_PLEDGE_REJECT = 1076
#到期确认卖出
OPT_AGREEMENT_REPURCHASE_EXPIRE_CONFIRM_SELL = 1077
# 债券分销
OPT_LOAN_DISTRIBUTION_BUY = 1078
#优先股竞价买入
OPT_PREFERENCE_SHARES_BIDDING_BUY = 1079
#优先股竞价卖出
OPT_PREFERENCE_SHARES_BIDDING_SELL = 1080
# 债券转托管
OPT_TOC_BOND = 1081
# 基金转托管
OPT_TOC_FUND = 1082
#同业拆入
OPT_IBANK_BORROW = 1083
#同业拆出
OPT_IBANK_LOAN = 1084
#拆入还款
OPT_IBANK_BORROW_REPAY = 1085
#拆出还款
OPT_IBANK_LOAN_REPAY = 1086
#理财产品申购
OPT_FINANCIAL_PRODUCT_BUY = 1087
#理财产品赎回
OPT_FINANCIAL_PRODUCT_SELL = 1088
#组合行权
OPT_OPTION_COMB_EXERCISE = 1089
#构建组合策略
OPT_OPTION_BUILD_COMB_STRATEGY = 1090
#解除组合策略
OPT_OPTION_RELEASE_COMB_STRATEGY = 1091
#协议回购逆回购提前终止申报确认
OPT_AGREEMENT_REPURCHASE_REVERSE_STOP_AHEAD_CONFIRM = 1092
#协议回购逆回购提前终止申报拒绝
OPT_AGREEMENT_REPURCHASE_REVERSE_STOP_AHEAD_REJECT = 1093
#协议回购逆回购方解除质押申报
OPT_AGREEMENT_REPURCHASE_REVERSE_RELEASE_PLEDGE = 1094
#协议回购逆回购解除质押申报确认
OPT_AGREEMENT_REPURCHASE_REVERSE_RELEASE_PLEDGE_CONFIRM = 1095
#协议回购逆回购解除质押申报拒绝
OPT_AGREEMENT_REPURCHASE_REVERSE_RELEASE_PLEDGE_REJECT = 1096
#债券投标
OPT_BOND_TENDER = 1097
#理财产品认购
OPT_FINANCIAL_PRODUCT_CALL = 1098
#北交所买入
OPT_NEEQ_O3B_CONTINUOUS_AUCTION_BUY = 1099
#北交所卖出
OPT_NEEQ_O3B_CONTINUOUS_AUCTION_SELL = 1100
#询价申报
OPT_NEEQ_O3B_ASK_PRICE = 1101
#申购申报
OPT_NEEQ_O3B_PRICE_CONFIRM = 1102
#大宗交易买入
OPT_NEEQ_O3B_BLOCKTRADING_BUY = 1103
#大宗交易卖出
OPT_NEEQ_O3B_BLOCKTRADING_SELL = 1104
#转融通非约定出借申报
OPT_LMT_LOAN_SET = 1105
#转融通约定出借申报
OPT_LMT_LOAN_CONVENTION = 1106
#转融通出借展期
OPT_LMT_LOAN_RENEWAL = 1107
#转融通出借提前了结
OPT_LMT_LOAN_SETTLE_EARLY = 1108
#跨市场ETF场内申购
OPT_CROSS_MARKET_IN_ETF_PURCHASE = 1109
#跨市场ETF场内赎回
OPT_CROSS_MARKET_IN_ETF_REDEMPTION = 1110
#跨市场ETF场外申购
OPT_CROSS_MARKET_OUT_ETF_PURCHASE = 1111
#跨市场ETF场外申购
OPT_CROSS_MARKET_OUT_ETF_REDEMPTION = 1112
#券源预约
OPT_CREDIT_APPOINTMENT = 1113
#网下申购-公开发行询价
OPT_OFF_IPO_PUB_PRICE = 1114
#网下申购-公开发行申购
OPT_OFF_IPO_PUB_PURCHASE = 1115
#网下申购-非公开发行询价
OPT_OFF_IPO_NON_PUB_PRICE = 1116
#网下申购-非公开发行申购
OPT_OFF_IPO_NON_PUB_PURCHASE = 1117
#债券回售
OPT_IBANK_PUT = 1118
#债券借贷融入
OPT_IBANK_BOND_BORROW = 1119
#债券借贷融出
OPT_IBANK_BOND_LEND = 1120
#债券借贷融入购回
OPT_IBANK_BOND_BORROW_REPAY = 1121
#债券借贷融出购回
OPT_IBANK_BOND_LEND_RETRIEVE = 1122
#债券借贷-质押券置换
OPT_IBANK_BOND_DISPLACE = 1123
#融券通-预约融券融入
OPT_LENDING_INTEGRATE_INTO = 1124
#融券通-预约融券融出
OPT_LENDING_MELT_OUT = 1125
#点击成交申报买入
OPT_FICC_MANUAL_DECLARE_BUY = 1126
#点击成交申报卖出
OPT_FICC_MANUAL_DECLARE_SELL = 1127
#点击成交确认买入
OPT_FICC_MANUAL_CONFIRM_BUY_CONFIRM = 1128
#点击成交拒绝买入
OPT_FICC_MANUAL_CONFIRM_BUY_REJECT = 1129
#点击成交确认卖出
OPT_FICC_MANUAL_CONFIRM_SELL_CONFIRM = 1130
#点击成交拒绝卖出
OPT_FICC_MANUAL_CONFIRM_SELL_REJECT = 1131
#协商成交申报买入
OPT_FICC_CONSULT_DECLARE_BUY = 1132
#协商成交申报卖出
OPT_FICC_CONSULT_DECLARE_SELL = 1133
#协商成交确认买入
OPT_FICC_CONSULT_CONFIRM_BUY_CONFIRM = 1134
#协商成交拒绝买入
OPT_FICC_CONSULT_CONFIRM_BUY_REJECT = 1135
#协商成交确认卖出
OPT_FICC_CONSULT_CONFIRM_SELL_CONFIRM = 1136
#协商成交拒绝卖出
OPT_FICC_CONSULT_CONFIRM_SELL_REJECT = 1137
#询价成交申报买入
OPT_FICC_ENQUIRY_DECLARE_BUY = 1138
#询价成交申报卖出
OPT_FICC_ENQUIRY_DECLARE_SELL = 1139
#询价成交报价回复确认买入
OPT_FICC_ENQUIRY_REPLAY_BUY_CONFIRM = 1140
#询价成交报价回复拒绝买入
OPT_FICC_ENQUIRY_REPLAY_BUY_REJECT = 1141
#询价成交报价回复确认卖出
OPT_FICC_ENQUIRY_REPLAY_SELL_CONFIRM = 1142
#询价成交报价回复拒绝卖出
OPT_FICC_ENQUIRY_REPLAY_SELL_REJECT = 1143
#询价成交询价成交确认买入
OPT_FICC_ENQUIRY_INQUIRY_BUY_CONFIRM = 1144
#询价成交询价成交拒绝买入
OPT_FICC_ENQUIRY_INQUIRY_BUY_REJECT = 1145
#询价成交询价成交确认卖出
OPT_FICC_ENQUIRY_INQUIRY_SELL_CONFIRM = 1146
#询价成交询价成交拒绝卖出
OPT_FICC_ENQUIRY_INQUIRY_SELL_REJECT = 1147
#竞买成交竞买预约买入
OPT_FICC_BINDDING_RESERVE_BUY = 1148
#竞买成交竞买预约卖出
OPT_FICC_BINDDING_RESERVE_SELL = 1149
#竞买成交竞买申报买入
OPT_FICC_BINDDING_DECLARE_BUY = 1150
#竞买成交竞买申报卖出
OPT_FICC_BINDDING_DECLARE_SELL = 1151
#竞买成交应价申报买入
OPT_FICC_BINDDING_PRICE_DECLARE_BUY = 1152
#竞买成交应价申报卖出
OPT_FICC_BINDDING_PRICE_DECLARE_SELL = 1153
#买入优先平仓
OPT_OPTION_BUY_CLOSE_THEN_OPEN = 1154
#卖出优先平仓
OPT_OPTION_SELL_CLOSE_THEN_OPEN = 1155
#资金划入
OPT_FUND_TRANSFER_IN = 1156
#资金划出
OPT_FUND_TRANSFER_OUT = 1157
#基金认购
OPT_STOCK_FUND_SUBSCRIBE = 1158
#现货买入
OPT_OTC_SPOT_GOODS_BUY = 1159
#现货卖出
OPT_OTC_SPOT_GOODS_SELL = 1160
#即时还券
OPT_TIMELY_SECU_REPAY = 1161
#专项即时还券
OPT_TIMELY_SECU_REPAY_SPECIAL = 1162
#协商成交合并确认买入
OPT_FICC_CONSULT_MERGE_BUY_CONFIRM = 1163
#协商成交合并拒绝买入
OPT_FICC_CONSULT_MERGE_BUY_REJECT = 1164
#协商成交合并确认卖出
OPT_FICC_CONSULT_MERGE_SELL_CONFIRM = 1165
#协商成交合并拒绝卖出
OPT_FICC_CONSULT_MERGE_SELL_REJECT = 1166
#买入交易解除申报
OPT_FICC_TRADECANCEL_DECLARE_BUY = 1167
#卖出交易解除申报
OPT_FICC_TRADECANCEL_DECLARE_SELL = 1168
#买入交易解除接受
OPT_FICC_TRADECANCEL_CONFIRM_BUY_CONFIRM = 1169
#买入交易解除拒绝
OPT_FICC_TRADECANCEL_CONFIRM_BUY_REJECT = 1170
#卖出交易解除接受
OPT_FICC_TRADECANCEL_CONFIRM_SELL_CONFIRM = 1171
#卖出交易解除拒绝
OPT_FICC_TRADECANCEL_CONFIRM_SELL_REJECT = 1172


DIRECTION_FLAG_BUY = 48 #买入
DIRECTION_FLAG_SELL = 49 #卖出
def getDirectionByOpType(opt):
    if opt in (
        OPT_BUY, OPT_OPEN_LONG, OPT_CLOSE_SHORT_TODAY_HISTORY_THEN_OPEN_LONG, OPT_CLOSE_SHORT_HISTORY_TODAY_THEN_OPEN_LONG,
        OPT_CLOSE_SHORT_TODAY, OPT_CLOSE_SHORT_HISTORY, OPT_CLOSE_SHORT_TODAY_FIRST, OPT_CLOSE_SHORT_HISTORY_FIRST,
        OPT_FIN_BUY, OPT_FIN_BUY_SPECIAL, OPT_BUY_SECU_REPAY, OPT_BUY_SECU_REPAY_SPECIAL, OPT_OPTION_BUY_CLOSE,
        OPT_OPTION_BUY_OPEN, OPT_OPTION_COVERED_CLOSE, OPT_OPTION_CALL_EXERCISE, OPT_N3B_PRICE_BUY, OPT_N3B_CONFIRM_BUY,
        OPT_N3B_REPORT_CONFIRM_BUY, OPT_N3B_LIMIT_PRICE_BUY, OPT_NEEQ_O3B_LIMIT_PRICE_BUY, OPT_FUND_SUBSCRIBE,
        OPT_FUND_PRICE_BUY, OPT_ETF_PURCHASE, OPT_FUND_MERGE, OPT_OUTER_BUY, OPT_IBANK_BOND_BUY, OPT_DISTRIBUTION_BUYING,
        OPT_IBANK_FUND_REPURCHASE, OPT_IBANK_BOND_REPAY, OPT_GOLD_PRICE_MIDDLE_BUY, OPT_GOLD_PRICE_DELIVERY_BUY,
        OPT_BLOCK_INTENTION_BUY, OPT_BLOCK_PRICE_BUY, OPT_BLOCK_CONFIRM_BUY, OPT_BLOCK_CONFIRM_MATCH_BUY,
        OPT_BLOCK_CLOSE_PRICE_BUY, OPT_COLLATERAL_TRANSFER_IN, OPT_N3B_CALL_AUCTION_BUY, OPT_N3B_AFTER_HOURS_BUY,
        OPT_CLOSE_SHORT, OPT_PLEDGE_OUT, OPT_AFTER_FIX_BUY, OPT_QUOTATION_REPURCHASE_BUY, OPT_OPTION_SECU_LOCK,
        OPT_NEEQ_O3B_CONTINUOUS_AUCTION_BUY, OPT_NEEQ_O3B_ASK_PRICE, OPT_NEEQ_O3B_PRICE_CONFIRM,
        OPT_NEEQ_O3B_BLOCKTRADING_BUY, OPT_TRANSACTION_IN_CASH_BUY, OPT_OUTRIGHT_REPO_FUND_REPURCHASE,
        OPT_OUTRIGHT_REPO_BOND_REPAY, OPT_AGREEMENT_REPURCHASE_TRANSACTION_DEC_FORWARD,
        OPT_AGREEMENT_REPURCHASE_ADVANCE_REPURCHASE, OPT_AGREEMENT_REPURCHASE_EXPIRE_RENEW,
        OPT_AGREEMENT_REPURCHASE_INTENTION_BUY, OPT_FINANCIAL_PRODUCT_BUY, OPT_FINANCIAL_PRODUCT_CALL,
        OPT_OPTION_RELEASE_COMB_STRATEGY, OPT_OTC_NON_CONTRACTUAL_DEPOSIT, OPT_OTC_CONTRACTUAL_DEPOSIT,
        OPT_OTC_FUND_SUBSCRIBE, OPT_OTC_FUND_PURCHASE, OPT_OTC_CONTRACTUAL_DEPOSIT_ASK, OPT_OTC_NON_CONTRACTUAL_DEPOSIT_ASK,
        OPT_CONVERT_BONDS, OPT_OFF_IPO_PUB_PRICE, OPT_OFF_IPO_PUB_PURCHASE, OPT_OFF_IPO_NON_PUB_PRICE,
        OPT_OFF_IPO_NON_PUB_PURCHASE, OPT_OPTION_BUY_CLOSE_THEN_OPEN, OPT_FICC_MANUAL_DECLARE_BUY,
        OPT_FICC_MANUAL_CONFIRM_BUY_CONFIRM, OPT_FICC_MANUAL_CONFIRM_BUY_REJECT, OPT_FICC_CONSULT_DECLARE_BUY,
        OPT_FICC_CONSULT_CONFIRM_BUY_CONFIRM, OPT_FICC_CONSULT_CONFIRM_BUY_REJECT, OPT_FICC_ENQUIRY_DECLARE_BUY,
        OPT_FICC_ENQUIRY_REPLAY_BUY_CONFIRM, OPT_FICC_ENQUIRY_REPLAY_BUY_REJECT, OPT_FICC_ENQUIRY_INQUIRY_BUY_CONFIRM,
        OPT_FICC_ENQUIRY_INQUIRY_BUY_REJECT, OPT_FICC_BINDDING_RESERVE_BUY, OPT_FICC_BINDDING_DECLARE_BUY,
        OPT_FICC_BINDDING_PRICE_DECLARE_BUY, OPT_FUND_TRANSFER_IN):
        return DIRECTION_FLAG_BUY
    else:
        return DIRECTION_FLAG_SELL

'''执行顺序'''
#主动腿比例优先
EESO_ActiveFirst = 0 
#同时报单
EESO_ConcurrentlyOrder = 1
#主动腿完全优先
EESO_ActiveFirstFull = 2

'''比较标志'''
#无效
EFHST_INVALID = -1
#大于
EFHST_GREATER = 0
#大于等于
EFHST_GREATER_EQUAL = 1
#小于
EFHST_LESS = 2
#小于等于
EFHST_LESS_EQUAL = 3
#无
EFHST_NONE_SYMBOL = 4

'''报价类型'''
PRTP_INVALID = -1 
#卖5
PRTP_SALE5 = 0
#卖4
PRTP_SALE4 = 1
#卖3
PRTP_SALE3 = 2
#卖2
PRTP_SALE2 = 3
#卖1
PRTP_SALE1 = 4
#最新价
PRTP_LATEST = 5
#买1
PRTP_BUY1 = 6
#买2
PRTP_BUY2 = 7
#买3
PRTP_BUY3 = 8
#买4
PRTP_BUY4 = 9
#买5
PRTP_BUY5 = 10
#指定价
PRTP_FIX = 11
#市价_涨跌停价
PRTP_MARKET = 12
#挂单价
PRTP_HANG = 13
#对手价
PRTP_COMPETE = 14

"""
划拨方向
"""
#/ *资金划拨* /
FUNDS_TRANSFER_NORMAL_TO_SPEED = 510 # 资金划拨-普通柜台到极速柜台
FUNDS_TRANSFER_SPEED_TO_NORMAL = 511 # 资金划拨-极速柜台到普通柜台
NODE_FUNDS_TRANSFER_SH_TO_SZ = 512 # 节点资金划拨-上海节点到深圳节点
NODE_FUNDS_TRANSFER_SZ_TO_SH = 513 # 节点资金划拨-深圳节点到上海节点
#/ *股份划拨* /
SECU_TRANSFER_NORMAL_TO_SPEED = 520 # 股份划拨-普通柜台划到极速柜台
SECU_TRANSFER_SPEED_TO_NORMAL = 521 # 股份划拨-极速柜台划到普通柜台

"""
股份划拨类型
"""
#股票账户客户划拨
TRANS_TRANSFER_SHARE = 0
#融资融券账户专项头寸划拨
TRANS_TRANSFER_SPECIAL_POSITIONS = 1
#融资融券账户客户划拨
TRANS_TRANSFER_CREDIT_SHARE = 2

"""
多空方向，股票不需要
"""
#多
DIRECTION_FLAG_LONG = 48
#空
DIRECTION_FLAG_SHORT = 49

"""
交易操作，用此字段区分股票买卖，期货开、平仓，期权买卖等
"""
OFFSET_FLAG_OPEN = 48
OFFSET_FLAG_CLOSE = 49
OFFSET_FLAG_FORCECLOSE = 50
OFFSET_FLAG_CLOSETODAY = 51
OFFSET_FLAG_ClOSEYESTERDAY = 52
OFFSET_FLAG_FORCEOFF = 53
OFFSET_FLAG_LOCALFORCECLOSE = 54