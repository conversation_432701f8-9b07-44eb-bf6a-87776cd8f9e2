--lua脚本相对于运行路径的相对路径
luaScriptPath = "../script/systemlua"
--vb脚本相对于运行路径的相对路径
vbScriptPath = "../script/vb"

--[[---------------------------------------------------------------
				金字塔和lua中的关键词
-----------------------------------------------------------------]]
keyword = {
	{key = "if", value = 1},
	{key = "then", value = 1},
	{key = "else", value = 1},
	{key = "while", value = 1},
	{key = "for", value = 1},
	{key = "to", value = 1},
	{key = "do", value = 1},
	{key = "input", value = 1},
	{key = "begin", value = 1},
	{key = "end", value = 1},
	{key = "variable", value = 1},
	{key = "true", value = 1},
	{key = "false", value = 1},
	{key = "break", value = 1},
	{key = "continue", value = 1},
	{key = "repeat", value = 1},
	{key = "until", value = 1},
	{key = "goto", value = 1},
	{key = "exit", value = 1},
	{key = "global", value = 1},
	--color --
	{key = "colorblack", value = 1},
	{key = "colorblue",  value = 1},
	{key = "colorbrown", value = 1},
	{key = "colorcyan",  value = 1},
	{key = "colorgray",  value = 1},
	{key = "colorgreen", value = 1},
	{key = "colormagenta",value = 1},
	{key = "colorred",   value = 1},
	{key = "colorwhite", value = 1},
	{key = "coloryellow",value = 1},
	{key = "colorblack", value = 1},
	{key = "colorblack", value = 1},
	{key = "colorblack", value = 1},
	{key = "colorblack", value = 1},
	{key = "colorlired", value = 1},
	{key = "colorlicyan", value = 1},

	--only in lua--
	{key = "return", value = 1},
	{key = "nil", value = 1},
	{key = "local", value = 1},
	{key = "in", value = 1},
	{key = "function", value = 1},
	{key = "elseif", value = 1},
	--系统保留的关键词，用于翻译时生成的一些中间变量--
	{key = "_ret", value = 11},
	{key = "func", value = 11},
	{key = "timetag", value = 11},
	{key = "", value = 0},
}
--[[--------------------------------------------------------------
	金字塔支持的数据引用周期，需要根据我们的实际情况进行调整
	stkindex是stkindi中用到的period index，其中缺失的是 16：节气线
----------------------------------------------------------------]]
period = {
	{name = "tick", time = 0, 				cycindex = 0,	stkindex = 0},
	{name = "sec1", time = 1000,			cycindex = 100, stkindex = -1},
	{name = "sec5", time = 5000,			cycindex = 101, stkindex = -1},
	{name = "min1", time = 60000, 			cycindex = 1, 	stkindex = 1},
	{name = "min3", time = 180000, 			cycindex = 17, 	stkindex = 17},
	{name = "min5", time = 300000,			cycindex = 2, 	stkindex = 2},
	{name = "min10",time = 600000, 			cycindex = 18, 	stkindex = 18},
	{name = "min15",time = 900000,			cycindex = 3, 	stkindex = 3},
	{name = "min30",time = 1800000, 		cycindex = 4, 	stkindex = 4},
	{name = "min60",time = 3600000, 		cycindex = 5, 	stkindex = 5},
	{name = "hour", time = 3600000,			cycindex = 102, stkindex = -1},
	{name = "day" , time = 86400000,		cycindex = 6, 	stkindex = 6},
	{name = "week", time = 604800000,		cycindex = 7, 	stkindex = 7},
	{name = "month",time = 2592000000,		cycindex = 8, 	stkindex = 8},
	{name = "quarter",time = 7776000000,	cycindex = 14, 	stkindex = 14},
	{name = "semiyear",time = 15552000000, 	cycindex = 15, 	stkindex = 15},
	{name = "year",time = 31536000000, 		cycindex = 9, 	stkindex = 9},
	{name = "multimin", time = 5400000,		cycindex = 11, 	stkindex = 11},
	{name = "multihour", time = 7200000,	cycindex = 13, 	stkindex = 13},
	{name = "multisec", time = 30000, 		cycindex = 12, 	stkindex = 12},
	{name = "multitick", time = 10, 		cycindex = 19, 	stkindex = 19},
	{name = "multiday", time = 0, 			cycindex = 10, 	stkindex = 10},
	{name = "default", time = -1, 			cycindex = -1, 	stkindex = -1},
}

--[[------------------------------------------------------------------
					系统预定义的公式
					参数类型说明：
						0	普通参数
						10  普通参数-数字类型
						20  普通参数-布尔类型
						1	引用参数，需要解析为函数
						2	颜色描述符
						3	线型描述符
						100 不定长
					函数类型(type)说明：
						0	行情函数
						1	数学函数
						2	逻辑函数
						3	控制函数
						4	引用函数
						5	统计函数
						6	绘图函数
						7	时间函数
						8	常数函数
						9	字符串函数
						10	交易系统
						11	指标函数
						12  动态行情
						13  系统函数
						14  扩展数据函数
						15  组合模型参数设置
						16  组合模型运行函数
						17  财务数据函数
						18  分级基金数据函数
						100	用户自定义
					函数附加参数类型 (epType) 说明：
						0	不附加参数
						1	只附加timetag一个参数
						2	附加timetag和formula两个参数
						3	只附加formula一个参数
					返回值类型说明：
						0	double
						1	int
						2	bool
						3	string
						4	vector<double>
						5	vector<int>
						6	vector<bool>
						7	vector<string>
						100 void
						200 uncertain
						1001 LuaStruct-ChildResult
						1002 LuaStruct-StockHoldingInfo
						1003 LuaStruct-CPositionDetail
						1004 LuaStruct-CDealDetail
						1005 LuaStruct-COrderDetail
						1006 LuaStruct-paramResult
						1007 LuaStruct-StockGroup
						1008 LuaStruct-priceVolumeData
                        1009 LuaStruct-OrderSignal
						
--------------------------------------------------------------------]]
formula = {
	-------------------------type = 0, 行情函数---------------------------------
	{name = "open",		argus = {},			 ret = 0,	 type = 0,   epType = 2,   leastParam = 0},
	{name = "o",		argus = {},			 ret = 0,	 type = 0,   epType = 2,   leastParam = 0},
	{name = "close",	argus = {},			 ret = 0,	 type = 0,   epType = 2,   leastParam = 0},
	{name = "c",		argus = {},			 ret = 0,	 type = 0,   epType = 2,   leastParam = 0},
	{name = "low",		argus = {},			 ret = 0,	 type = 0,   epType = 2,   leastParam = 0},
	{name = "l",		argus = {},			 ret = 0,	 type = 0,   epType = 2,   leastParam = 0},
	{name = "high",		argus = {},			 ret = 0,	 type = 0,   epType = 2,   leastParam = 0},
	{name = "h",		argus = {},			 ret = 0,	 type = 0,   epType = 2,   leastParam = 0},
	{name = "vol",		argus = {},			 ret = 0,	 type = 0,   epType = 2,   leastParam = 0},
	{name = "v",		argus = {},			 ret = 0,	 type = 0,   epType = 2,   leastParam = 0},
	{name = "iperiod",	argus = {},			 ret = 0,	 type = 11,   epType = 2,   leastParam = 0},
    {name = "period",	argus = {},			 ret = 0,	 type = 0,   epType = 2,   leastParam = 0},
	{name = "settleprice",	argus = {},		 ret = 0,	 type = 0,   epType = 2,   leastParam = 0},
	{name = "settlement",	argus = {},		 ret = 0,	 type = 0,   epType = 2,   leastParam = 0},
	{name = "amount",	argus = {},			 ret = 0,	 type = 0,	 epType = 2,   leastParam = 0},
	{name = "askprice", argus = {10}, 		 ret = 0,	 type = 0,   epType = 2,   leastParam = 0},
	{name = "askvol", 	argus = {10},		 ret = 0,	 type = 0,   epType = 2,   leastParam = 0},
	{name = "bidprice", argus = {10}, 		 ret = 0,	 type = 0,   epType = 2,   leastParam = 0},
	{name = "bidvol", 	argus = {10},		 ret = 0,	 type = 0,   epType = 2,   leastParam = 0},
	{name = "bvol",	argus = {},			 ret = 0,	 type = 0,   epType = 2,   leastParam = 0},
	{name = "svol",	argus = {},			 ret = 0,	 type = 0,   epType = 2,   leastParam = 0},
	{name = "indexc", 	argus = {},			 ret = 0,	 type = 0,   epType = 2,   leastParam = 0},
	{name = "indexo", 	argus = {},			 ret = 0,	 type = 0,   epType = 2,   leastParam = 0},
	{name = "indexh", 	argus = {},			 ret = 0,	 type = 0,   epType = 2,   leastParam = 0},
	{name = "indexl", 	argus = {},			 ret = 0,	 type = 0,   epType = 2,   leastParam = 0},
	{name = "indexv", 	argus = {},			 ret = 0,	 type = 0,   epType = 2,   leastParam = 0},
	{name = "indexa", 	argus = {},			 ret = 0,	 type = 0,   epType = 2,   leastParam = 0},
	{name = "oopen", 	argus = {},			 ret = 0,	 type = 0,   epType = 2,   leastParam = 0},
	{name = "oclose", 	argus = {},			 ret = 0,	 type = 0,   epType = 2,   leastParam = 0},
	{name = "ohigh", 	argus = {},			 ret = 0,	 type = 0,   epType = 2,   leastParam = 0},
	{name = "olow", 	argus = {},			 ret = 0,	 type = 0,   epType = 2,   leastParam = 0},
	{name = "ovol", 	argus = {},			 ret = 0,	 type = 0,   epType = 2,   leastParam = 0},
	{name = "oamount", 	argus = {},			 ret = 0,	 type = 0,   epType = 2,   leastParam = 0},
	{name = "osettleprice", argus = {},	     ret = 0,	 type = 0,   epType = 2,   leastParam = 0},
	--{name = "opena", 	argus = {},			 ret = 0,	 type = 0,   epType = 2,   leastParam = 0},
	{name = "openint", 	argus = {},			 ret = 0,	 type = 0,   epType = 2,   leastParam = 0},
	--{name = "openv", 	argus = {},			 ret = 0,	 type = 0,   epType = 2,   leastParam = 0},
	{name = "qt", 		argus = {},			 ret = 0,	 type = 0,   epType = 2,   leastParam = 0},
	{name = "callstock",argus = {0,0,0,0},	 ret = 0,    type = 0,   epType = 2,   leastParam = 2},
	{name = "callstock2",argus = {0,0,0,0},	 ret = 0,    type = 100,   epType = 2,   leastParam = 4},
	{name = "rtclose",	argus = {},			 ret = 0,	 type = 0,   epType = 2,   leastParam = 0},
	{name = "rtvol",	argus = {},			 ret = 0,	 type = 0,   epType = 2,   leastParam = 0},
	{name = "estimatedprice",	argus = {},	ret = 0,	 type = 0,   epType = 2,   leastParam = 0},
	---------------获取当日最新数据的函数--------------------
	{name = "tickopen", 	argus = {},			 ret = 0,	 type = 0,   epType = 2,   leastParam = 0},
	{name = "ticklast", 	argus = {},			 ret = 0,	 type = 0,   epType = 2,   leastParam = 0},
	{name = "tickhigh", 	argus = {},			 ret = 0,	 type = 0,   epType = 2,   leastParam = 0},
	{name = "ticklow", 	argus = {},			 ret = 0,	 type = 0,   epType = 2,   leastParam = 0},
	{name = "tickamount", 	argus = {},			 ret = 0,	 type = 0,   epType = 2,   leastParam = 0},
	{name = "tickvol", 	argus = {},			 ret = 0,	 type = 0,   epType = 2,   leastParam = 0},
	{name = "ticklastclose", 	argus = {},		 ret = 0,	 type = 0,   epType = 2,   leastParam = 0},
	{name = "tickopenint", 	argus = {},		 ret = 0,	 type = 0,   epType = 2,   leastParam = 0},
	{name = "tickpe",		argus = {}, 		 ret = 0, 	 type = 0,	 epType = 2,   leastParam = 0},
	{name = "transaction",	argus = {}, 	 ret = 0, 	 type = 0,	 epType = 2,   leastParam = 0},
	{name = "ticktransaction",	argus = {}, 	 ret = 0, 	 type = 0,	 epType = 2,   leastParam = 0},
	-------------------------type = 1, 数学函数---------------------------------
	{name = "max",		argus = {10, 10},		 ret = 0,	 type = 1,   epType = 0,   leastParam = 2},
	{name = "min",		argus = {10, 10},		 ret = 0,	 type = 1,   epType = 0,   leastParam = 2},
	{name = "abs",		argus = {10},		 ret = 0,	 type = 1,   epType = 0,   leastParam = 1},
	{name = "intpart",	argus = {10},		 ret = 1,	 type = 1,   epType = 0,   leastParam = 1},
	{name = "log",		argus = {10},		 ret = 0,	 type = 1,   epType = 0,   leastParam = 1},
	{name = "rounds",	argus = {10, 10},		 ret = 0,	 type = 1,   epType = 0,   leastParam = 2},
	{name = "acos",		argus = {10},		 ret = 0,	 type = 1,   epType = 0,   leastParam = 1},
	{name = "asin",		argus = {10},		 ret = 0,	 type = 1,   epType = 0,   leastParam = 1},
	{name = "atan",		argus = {10},		 ret = 0,	 type = 1,   epType = 0,   leastParam = 1},
	{name = "cos",		argus = {10},		 ret = 0,	 type = 1,   epType = 0,   leastParam = 1},
	{name = "sin",		argus = {10},		 ret = 0,	 type = 1,   epType = 0,   leastParam = 1},
	{name = "tan",		argus = {10},		 ret = 0,	 type = 1,   epType = 0,   leastParam = 1},
	{name = "sqrt",		argus = {10},		 ret = 0,	 type = 1,   epType = 0,   leastParam = 1},
	{name = "ceiling",	argus = {10},		 ret = 0,	 type = 1,   epType = 0,   leastParam = 1},
	{name = "exp",		argus = {10},		 ret = 0,	 type = 1,   epType = 0,   leastParam = 1},
	{name = "floor",	argus = {10},		 ret = 0,	 type = 1,   epType = 0,   leastParam = 1},
	{name = "ln",		argus = {10},		 ret = 0,	 type = 1,   epType = 0,   leastParam = 1},
	{name = "fracpart",	argus = {10},		 ret = 0,	 type = 1,   epType = 0,   leastParam = 1},
	{name = "mod",		argus = {10,10},		 ret = 0,	 type = 1,   epType = 0,   leastParam = 2},
	{name = "pow",		argus = {10,10},		 ret = 0,	 type = 1,   epType = 0,   leastParam = 2},
	{name = "reverse",	argus = {10},		 ret = 0,	 type = 1,   epType = 0,   leastParam = 1},
	{name = "round",	argus = {10},		 ret = 0,	 type = 1,   epType = 0,   leastParam = 1},
	{name = "sgn",		argus = {10},		 ret = 0,	 type = 1,   epType = 0,   leastParam = 1},
	{name = "rand",		argus = {10},		 ret = 0,	 type = 1,   epType = 0,   leastParam = 1},
	{name = "combin",	argus = {10,10},		 ret = 0,	 type = 1,   epType = 1,   leastParam = 2},
	-------------------------type = 2, 逻辑函数---------------------------------
	{name = "all",		argus = {20, 10},		 ret = 2,	 type = 2,   epType = 1,   leastParam = 2},
	{name = "every",		argus = {20, 10},		 ret = 2,	 type = 2,   epType = 1,   leastParam = 2},
	{name = "iff",		argus = {20,0,0},	 ret = 200,	 type = 2,   epType = 0,   leastParam = 3},
	{name = "ifelse",	argus = {20,0,0},	 ret = 200,	 type = 2,   epType = 0,   leastParam = 3},
	{name = "not",		argus = {20},		 ret = 2,	 type = 2,   epType = 2,   leastParam = 1},--lua 有个not运算符
	{name = "valuewhen",argus = {20,0},		 ret = 0,	 type = 2,   epType = 1,   leastParam = 2},
	{name = "any",		argus = {20,10},		 ret = 2,	 type = 2,   epType = 1,   leastParam = 2},
	{name = "exist",	argus = {20,10},		 ret = 2,	 type = 2,   epType = 1,   leastParam = 2},
	{name = "cross", 	argus = {10,10}, 	 	 ret = 2,	 type = 2,   epType = 1,   leastParam = 2},
	{name = "valid", 	argus = {0}, 	 	 ret = 2,	 type = 2,   epType = 0,   leastParam = 1},
	{name = "between", 	argus = {10,10,10}, 	 ret = 2,	 type = 2,   epType = 0,   leastParam = 3},
	{name = "ifn", 		argus = {20,0,0}, 	 ret = 200,	 type = 2,   epType = 0,   leastParam = 3},
	{name = "isdown", 	argus = {}, 	 	 ret = 2,	 type = 2,   epType = 2,   leastParam = 0},
	{name = "isequal", 	argus = {}, 	 	 ret = 2,	 type = 2,   epType = 2,   leastParam = 0},
	{name = "isup", 	argus = {}, 	 	 ret = 2,	 type = 2,   epType = 2,   leastParam = 0},
	{name = "longcross",argus = {10,10,10}, 	 ret = 2,	 type = 2,   epType = 1,   leastParam = 3},
	{name = "islastbar", argus = {}, 		 ret = 2,	 type = 2,   epType = 2,   leastParam = 0},
	{name = "last",		argus = {20,10,10},	 ret = 2,	 type = 2,   epType = 1,   leastParam = 3},
	{name = "range",	argus = {10,10,10},	 ret = 2,	 type = 2,   epType = 0,   leastParam = 3},
	{name = "suspend",	argus = {10},	 ret = 0,	 type = 2,   epType = 2,   leastParam = 0},
	{name = "setdataalignmode",	argus = {10},	 ret = 100,	 type = 2,   epType = 3,   leastParam = 1},
	{name = "unitofquantity", argus = {0}, ret = 1, type = 2, epType = 3, leastParam = 1},
	{name = "equalweightindex", argus = {0}, ret = 3, type = 2, epType = 3, leastParam = 1},
	{name = "isindexorglr", argus = {0}, ret = 1, type = 2, epType = 3, leastParam = 1},
	{name = "isetfcode", argus = {0}, ret = 1, type = 2, epType = 3, leastParam = 1},
	{name = "isindexcode", argus = {0}, ret = 1, type = 2, epType = 3, leastParam = 1},
	{name = "isfuturecode", argus = {0}, ret = 1, type = 2, epType = 3, leastParam = 1},
	-------------------------type = 4, 引用函数---------------------------------
	{name = "sma",		argus = {10, 10, 10},	 ret = 0,	 type = 4,   epType = 1,   leastParam = 3},
	{name = "ref",		argus = {10, 10},		 ret = 0,	 type = 4,   epType = 1,   leastParam = 2},
	{name = "barslast", argus = {20},		 ret = 0,	 type = 4,   epType = 1,   leastParam = 1},
	{name = "barslasts", argus = {20, 10},		 ret = 0,	 type = 4,   epType = 1,   leastParam = 2},
	{name = "sum",		argus = {10, 10},	 ret = 0,	 type = 4,   epType = 1,   leastParam = 2},
	{name = "hhv",		argus = {10, 10},		 ret = 0,	 type = 4,   epType = 1,   leastParam = 2},
	{name = "count",	argus = {20, 10},		 ret = 1,	 type = 4,   epType = 1,   leastParam = 2},
	{name = "ma",		argus = {10, 10},		 ret = 0,	 type = 4,   epType = 1,   leastParam = 2},
	{name = "xma",		argus = {1, 10},		 ret = 0,	 type = 4,   epType = 2,   leastParam = 2},
	{name = "ima",		argus = {10,10,10},	 ret = 0,	 type = 4,   epType = 1,   leastParam = 3},
	{name = "dma",		argus = {10, 10},		 ret = 0,	 type = 4,   epType = 1,   leastParam = 2},
	{name = "ema",		argus = {10, 10},		 ret = 0,	 type = 4,   epType = 1,   leastParam = 2},
	{name = "tma",		argus = {10, 10, 10},	 ret = 0,	 type = 4,   epType = 1,   leastParam = 3},
	{name = "filter",	argus = {10, 10},		 ret = 0,	 type = 4,   epType = 1,   leastParam = 2},
	{name = "llv",		argus = {10, 10},		 ret = 0,	 type = 4,   epType = 1,   leastParam = 2},
	{name = "barscount",argus = {10},		 ret = 1,	 type = 4,   epType = 1,   leastParam = 1},
	{name = "barssince",argus = {20},		 ret = 1,	 type = 4,   epType = 1,   leastParam = 1},
	{name = "barssincen",argus = {20,10},		 ret = 1,	 type = 4,   epType = 1,   leastParam = 2},
	{name = "currbarscount", argus= {},		 ret = 1,	 type = 4,   epType = 2,   leastParam = 0},
	{name = "hhvbars",	argus = {10,10},		 ret = 1,	 type = 4,   epType = 1,   leastParam = 2},
	{name = "llvbars",  argus = {10,10},		 ret = 1,	 type = 4,   epType = 1,   leastParam = 2},
	{name = "sfilter",  argus = {20,20},		 ret = 2,	 type = 4,   epType = 1,   leastParam = 2},
	{name = "tr", 		argus = {},			 ret = 0,	 type = 4,   epType = 2,   leastParam = 0},
	{name = "trma",		argus = {10,10},		 ret = 0,	 type = 4,   epType = 1,   leastParam = 2},
	{name = "wma",		argus = {10,10},		 ret = 0,	 type = 4,   epType = 1,   leastParam = 2},
	{name = "todaybar", argus = {},			 ret = 1,	 type = 4,   epType = 2,   leastParam = 0},
	{name = "ret", 		argus = {10,10},		 ret = 0,	 type = 4,   epType = 2,   leastParam = 2},
	{name = "newhbars", argus = {10,10},		 ret = 1,	 type = 4,   epType = 1,   leastParam = 2},
	{name = "newlbars", argus = {10,10},		 ret = 1,	 type = 4,   epType = 1,   leastParam = 2},
	{name = "refdate",  argus = {1,10,10},	 ret = 0,	 type = 4,   epType = 2,   leastParam = 2},
	{name = "hod",		argus = {10,10},		 ret = 1,	 type = 4,   epType = 1,   leastParam = 2},
	{name = "lod",		argus = {10,10},		 ret = 1,	 type = 4,   epType = 1,   leastParam = 2},
	{name = "sumbars",	argus = {10,10},		 ret = 1,	 type = 4,   epType = 1,   leastParam = 2},
	{name = "drawnull",	argus = {},			 ret = 0,	 type = 4,   epType = 0,   leastParam = 0},
	{name = "barpos",	argus = {},			 ret = 1,	 type = 4,   epType = 2,   leastParam = 0},
	{name = "market",	argus = {},			 ret = 0,	 type = 4,   epType = 2,   leastParam = 0},
	{name = "refx",  	argus = {1,10},	     ret = 0,	 type = 4,   epType = 2,   leastParam = 2},
	{name = "refparam", argus = {0,0},	     ret = 0,	 type = 4,   epType = 2,   leastParam = 1},
	{name = "median",  	argus = {10,10},	     ret = 0,	 type = 4,   epType = 2,   leastParam = 2},
	{name = "stkindi", 	argus = {0,0,10,10,10,10},	 ret = 0,	 type = 4,   epType = 0,   leastParam = 4},
	{name = "backset",  argus = {20,10},	     ret = 0,	 type = 4,   epType = 2,   leastParam = 2},
	{name = "backsetx",  argus = {20,10,0},	     ret = 0,	 type = 4,   epType = 2,   leastParam = 3},
	--{name = "fliterx",  argus = {1,0},	     ret = 0,	 type = 4,   epType = 2,   leastParam = 2}, -- problems
	--{name = "setval",  argus = {1,0},	     ret = 0,	 type = 4,   epType = 2,   leastParam = 2}, -- problems
	{name = "setrefxnum",  	argus = {0},	     ret = 100,	 type = 4,   epType = 2,   leastParam = 1},
    {name = "barsnext", argus = {20}, ret = 1, type = 4, epType = 2, leastParam = 1},
    {name = "dopen", argus = {}, ret = 0, type = 4, epType = 2, leastParam = 0},
    {name = "dhigh", argus = {}, ret = 0, type = 4, epType = 2, leastParam = 0},
    {name = "dlow", argus = {}, ret = 0, type = 4, epType = 2, leastParam = 0},
    {name = "dclose", argus = {}, ret = 0, type = 4, epType = 2, leastParam = 0},
    {name = "dvol", argus = {}, ret = 0, type = 4, epType = 2, leastParam = 0},
    {name = "barslastcount", argus = {20}, ret = 1, type = 4, epType = 2, leastParam = 1},
    {name = "mema",		argus = {10, 10},		 ret = 0,	 type = 4,   epType = 1,   leastParam = 2},
    {name = "hod2", argus = {0, 10, 10}, ret = 1, type = 4, epType = 0, leastParam = 3},
	-------------------------type = 6, 绘图函数---------------------------------
	{name = "kline",	argus = {10,10,10,10,0}, ret = 100,	 type = 6,   epType = 0,   leastParam = 5},
	{name = "drawtext",	argus = {20,10,0,2,0}, ret = 100,	 type = 6,   epType = 0,   leastParam = 3},
	--{name = "drawtextex",argus = {20,10,10,10,0,2}, ret = 100, type = 6,   epType = 0,   leastParam = 5},
	{name = "barsset",	argus = {20,10,10,10}, 	 ret = 100,	 type = 6,   epType = 0,   leastParam = 4},
	--{name = "drawarc",  argus = {20,10,20,10,0,0,2,0,3}, ret = 100, type = 6, epType = 0, leastParam = 6},
	{name = "drawbmp",	argus = {20,10,0,0},   ret = 100,	 type = 6,   epType = 0,   leastParam = 3},
	{name = "drawline", argus = {20,10,20,10,0,2,0,3}, ret = 100, type = 6, epType = 0, leastParam = 5},
	{name = "vertline", argus = {20,10,10,2,4,3},ret = 100, type = 6,   epType = 0,   leastParam = 1},
	--{name = "stickline",argus = {20,10,10,0,0,2},ret = 100, type = 6,   epType = 0,   leastParam = 5},
	--{name = "partline", argus = {20,10,2,0,3}, ret = 100,	 type = 6,   epType = 0,   leastParam = 2},
	{name = "colorrgb", argus = {10,10,10},	 ret = 1,	 type = 6,   epType = 0,   leastParam = 3},
	{name = "drawnumber",argus= {20,10,10,10,2,0},ret =100,  type = 6,   epType = 0,   leastParam = 4},
	{name = "drawicon", argus = {20,10,0,0},	 ret = 100,  type = 6,	 epType = 0,   leastParam = 3},
	{name = "drawband",  argus =  {10,2,10,2},  ret = 100,  type = 6,  epType = 0,  leastParam = 4},
	{name = "drawgbk_div",  argus =  {20,2,2,0,0},  ret = 100,  type = 6,  epType = 0,  leastParam = 5},
	{name = "drawstick",  argus =  {0, 0, 10, 2, 2},  ret = 100,  type = 6,  epType = 0,  leastParam = 5},
	{name = "drawarrow",  argus =  {0, 0, 0, 0, 0, 0, 2},  ret = 100,  type = 6,  epType = 0,  leastParam = 7},
	{name = "drawrectangle",  argus =  {0, 0, 0, 0, 0, 0, 2},  ret = 100,  type = 6,  epType = 0,  leastParam = 7},
    {name = "drawtext_fix",	argus = {20,10,10,10,0}, ret = 100,	 type = 6,   epType = 0,   leastParam = 5},
    {name = "stickline",  argus =  {20,10,10,10,10},  ret = 100,  type = 6,  epType = 0,  leastParam = 5},
	-------------------------type = 7, 时间函数---------------------------------
	{name = "date", 	argus = {}, 		 ret = 1,	 type = 7,   epType = 2,   leastParam = 0},
	{name = "ndate", 	argus = {}, 		 ret = 1,	 type = 7,   epType = 2,   leastParam = 0},
	{name = "tdate", 	argus = {}, 		 ret = 1,	 type = 7,   epType = 2,   leastParam = 0},
	{name = "hour", 	argus = {}, 		 ret = 1,	 type = 7,   epType = 2,   leastParam = 0},
	{name = "year", 	argus = {},	 	 	 ret = 1,	 type = 7,   epType = 2,   leastParam = 0},
	{name = "day",		argus = {},			 ret = 1,	 type = 7,   epType = 2,   leastParam = 0},
	{name = "minute",	argus = {},			 ret = 1,	 type = 7,   epType = 2,   leastParam = 0},
	{name = "month",	argus = {},			 ret = 1,	 type = 7,   epType = 2,   leastParam = 0},
	{name = "quarter",	argus = {},			 ret = 1,	 type = 7,   epType = 2,   leastParam = 0},
	{name = "time", 	argus = {},	 	 	 ret = 1,	 type = 7,   epType = 2,   leastParam = 0},
	{name = "ntime", 	argus = {},	 	 	 ret = 1,	 type = 7,   epType = 2,   leastParam = 0},
	{name = "weekday",  argus = {},	 	 	 ret = 1,	 type = 7,   epType = 2,   leastParam = 0},
	{name = "tweekday",  argus = {},	 	 	 ret = 1,	 type = 7,   epType = 2,   leastParam = 0},
	{name = "timerat",  argus = {10,10},	 ret = 0,	 type = 7,   epType = 2,   leastParam = 2},
	{name = "timerafter",  argus = {10,10,10},	 	 ret = 0,	 type = 7,   epType = 2,   leastParam = 3},
	{name = "currentdate",argus = {},		 ret = 1,	 type = 7,   epType = 0,   leastParam = 0},
	{name = "currenttime",argus = {},		 ret = 1,	 type = 7,   epType = 0,   leastParam = 0},
	{name = "todaymilliseconds",argus = {},		 ret = 1,	 type = 7,   epType = 0,   leastParam = 0},
	{name = "datediff",	argus = {10,10},		 ret = 1,	 type = 7,   epType = 0,   leastParam = 2},
	{name = "datetod1970",argus = {10},		 ret = 1,	 type = 7,   epType = 0,   leastParam = 1},
	{name = "dayofweek",argus = {10},		 ret = 1,	 type = 7,   epType = 0,   leastParam = 1},
	{name = "days1970", argus = {},		 	 ret = 1,	 type = 7,   epType = 2,   leastParam = 0},
	{name = "t0totime", argus = {10},	 	 ret = 1,	 type = 7,   epType = 0,   leastParam = 1},
	{name = "timetot0", argus = {10},	 	 ret = 1,	 type = 7,   epType = 0,   leastParam = 1},
	{name = "time0", 	argus = {},	 	 	 ret = 1,	 type = 7,   epType = 2,   leastParam = 0},
	{name = "barpos",	argus = {},			 ret = 1,	 type = 7,   epType = 1,   leastParam = 0},
	{name = "barstatus",argus = {},			 ret = 1,	 type = 7,   epType = 2,   leastParam = 0},
	{name = "d1970todate",argus = {10},		 ret = 1,	 type = 7,   epType = 0,   leastParam = 1},
	{name = "datepos",	argus = {0},		 ret = 1,	 type = 7,   epType = 2,   leastParam = 1},
	{name = "openminutes",argus = {10},	 	 ret = 1,	 type = 7,   epType = 2,   leastParam = 1},
	{name = "nextholidaydif",argus = {0},	 ret = 1,	 type = 7,   epType = 2,   leastParam = 1},
	{name = "nextholidaydifspec",argus = {0,0},	 ret = 1,	 type = 7,   epType = 2,   leastParam = 1},
	{name = "nextmonthdaydif",argus = {0},	 ret = 1,	 type = 7,   epType = 2,   leastParam = 1},
	{name = "timefromstart",argus = {},		 ret = 1,	 type = 7,   epType = 2,   leastParam = 0},
    {name = "currenttradedate",argus = {0},	 ret = 1,	 type = 7,   epType = 2,   leastParam = 1},
    {name = "inbar",	argus = {10,10}, ret = 2, type = 7, epType = 2, leastParam = 2},
    {name = "datetoday",argus = {10},		 ret = 1,	 type = 7,   epType = 0,   leastParam = 1},
	-------------------------type = 8, 常数函数---------------------------------
	{name = "capital",	argus = {},			 ret = 0,	 type = 8,   epType = 2,   leastParam = 0},
	{name = "capitalTotal",	argus = {},			 ret = 0,	 type = 8,   epType = 2,   leastParam = 0},
	{name = "datacount", argus = {}, 		 ret = 1,	 type = 8,   epType = 2,   leastParam = 0},
	{name = "closetime", argus = {10}, 		 ret = 1,	 type = 8,   epType = 2,   leastParam = 1},
	{name = "datatype", argus = {}, 		 ret = 1,	 type = 8,   epType = 2,   leastParam = 0},
	{name = "datatypemore",argus={10}, 		 ret = 0,	 type = 8,   epType = 0,   leastParam = 1},
	{name = "findindexbytime",argus={0},	 ret = 0,	 type = 8,   epType = 2,   leastParam = 1},
	{name = "mindiff", 	argus = {},			 ret = 0,	 type = 8,   epType = 2,   leastParam = 0},
	{name = "opentime", argus = {10},		 ret = 1,	 type = 8,   epType = 2,   leastParam = 1},
	{name = "volunit", argus = {}, 			 ret = 0,	 type = 8,   epType = 2,   leastParam = 0},
	{name = "getinstrumentdetail", argus = {0, 0}, 		 ret = 3,	 type = 8,   epType = 2,   leastParam = 2},
	{name = "limitupperformance", argus = {0, 0}, 		 ret = 0,	 type = 8,   epType = 2,   leastParam = 2},
	{name = "fundnetvalue", argus = {0, 0}, 		 ret = 0,	 type = 8,   epType = 2,   leastParam = 2},
	-------------------------type = 9, 字符串函数--------------------------------
	{name = "lowerstr", argus = {0}, 		 ret = 3, 	 type = 9,   epType = 0,   leastParam = 1},
	{name = "upperstr", argus = {0},		 ret = 3,	 type = 9,   epType = 0,   leastParam = 1},
	{name = "strlen", 	argus = {0},		 ret = 1,	 type = 9,   epType = 0,   leastParam = 1},
	{name = "strleft", 	argus = {0,0},		 ret = 3,	 type = 9,   epType = 0,   leastParam = 2},
	{name = "strmid", 	argus = {0,0,0},	 ret = 3,	 type = 9,   epType = 0,   leastParam = 3},
	{name = "strright", argus = {0,0},		 ret = 3,	 type = 9,   epType = 0,   leastParam = 2},
	{name = "ltrim", 	argus = {0}, 		 ret = 3, 	 type = 9,   epType = 0,   leastParam = 1},
	{name = "rtrim", 	argus = {0}, 		 ret = 3, 	 type = 9,   epType = 0,   leastParam = 1},
	{name = "numtostr", argus = {0,0},		 ret = 3,	 type = 9,   epType = 0,   leastParam = 2},
	{name = "strcat", 	argus = {0,0}, 		 ret = 3, 	 type = 9,   epType = 0,   leastParam = 2},
	{name = "strtonum", argus = {0},		 ret = 0,	 type = 9,   epType = 0,   leastParam = 1},
	{name = "strtonumex", argus = {0,0},		 ret = 0,	 type = 9,   epType = 0,   leastParam = 2},
	{name = "strinsert",argus = {0,0,0}, 	 ret = 3, 	 type = 9,   epType = 0,   leastParam = 3},
	{name = "strremove",argus = {0,0,0},	 ret = 3,	 type = 9,   epType = 0,   leastParam = 3},
	{name = "strfind", 	argus = {0,0,0}, 	 ret = 1, 	 type = 9,   epType = 0,   leastParam = 3},
	{name = "strreplace",argus = {0,0,0},	 ret = 3,	 type = 9,   epType = 0,   leastParam = 3},
	{name = "strtrimleft",argus = {0,0},	 ret = 3,	 type = 9,   epType = 0,   leastParam = 2},
	{name = "strtrimright",argus = {0,0},	 ret = 3,	 type = 9,   epType = 0,   leastParam = 2},
	{name = "strcmp", 	argus = {0,0}, 		 ret = 1, 	 type = 9,   epType = 0,   leastParam = 2},
	{name = "stricmp", 	argus = {0,0}, 		 ret = 1, 	 type = 9,   epType = 0,   leastParam = 2},
	{name = "strncmp", 	argus = {0,0,0}, 	 ret = 1, 	 type = 9,   epType = 0,   leastParam = 3},
	{name = "stringtofile", argus = {0,0},	 ret = 100,	 type = 9,   epType = 0,   leastParam = 2},
	{name = "stklabel", argus = {},			 ret = 3,	 type = 9,   epType = 2,   leastParam = 0},
	{name = "marketlabel",argus = {},		 ret = 3,	 type = 9,   epType = 2,   leastParam = 0},
	{name = "formulaname",argus = {},		 ret = 3,	 type = 9,   epType = 2,   leastParam = 0},
	{name = "blkname", 	argus = {},		 ret = 3,	 type = 9,   epType = 3,   leastParam = 0},
	{name = "findblock", 	argus = {0},		 ret = 3,	 type = 9,   epType = 3,   leastParam = 1},
	{name = "findindex", 	argus = {0, 0},		 ret = 3,	 type = 9,   epType = 2,   leastParam = 2},
	{name = "switchindex", 	argus = {0, 0},		 ret = 3,	 type = 9,   epType = 2,   leastParam = 2},
	{name = "convfuture", 	argus = {0, 0},		 ret = 3,	 type = 9,   epType = 3,   leastParam = 2},
	{name = "marketname",argus = {},		 ret = 3,	 type = 9,   epType = 2,   leastParam = 0},
	{name = "marketlabel1",argus = {},		 ret = 3,	 type = 9,   epType = 2,   leastParam = 0},
	{name = "stkname", 	argus = {0},			 ret = 3,	 type = 9,   epType = 2,   leastParam = 0},
    {name = "timestamptostr",argus = {10},	 ret = 3,	 type = 9,   epType = 0,   leastParam = 1},
    {name = "strtotimestamp",argus = {0},	 ret = 1,	 type = 9,   epType = 0,   leastParam = 1},
    {name = "findblocklist", argus = {0}, ret = 1007, type = 9, epType = 3, leastParam = 1},
    {name = "stockcode",argus = {},		 ret = 3,	 type = 9,   epType = 2,   leastParam = 0},
    {name = "stgname",argus = {},		 ret = 3,	 type = 9,   epType = 2,   leastParam = 0},
	--------------------to do fomula-----------------------
	--{name = "enginecode",argus = {},		 ret = 3,	 type = 9,   epType = 2,   leastParam = 0},
	--{name = "username", argus = {},			 ret = 3,	 type = 9,   epType = 2,   leastParam = 0},
	{name = "inblock", 	argus = {0},		 ret = 1,	 type = 9,   epType = 2,   leastParam = 1},
	{name = "inblock2", 	argus = {0,0},		 ret = 1,	 type = 9,   epType = 2,   leastParam = 1},
	{name = "get_external_data_single_number", 	argus = {0,0,0},		 ret = 0,	 type = 9,   epType = 0,   leastParam = 3},
	{name = "get_external_data_single_string", 	argus = {0,0,0},		 ret = 3,	 type = 9,   epType = 0,   leastParam = 3},
	{name = "get_external_data_kline", 	argus = {0,0,0,0},		 ret = 0,	 type = 9,   epType = 0,   leastParam = 4},
	{name = "blocksize", 	argus = {0,0},		 ret = 1,	 type = 9,   epType = 2,   leastParam = 1},
	{name = "stockbyblockrank", 	argus = {0,0,0},		 ret = 3,	 type = 9,   epType = 2,   leastParam = 3},
	{name = "blocksum", 	argus = {0,0},		 ret = 0,	 type = 9,   epType = 2,   leastParam = 2},
    {name = "fmt", argus = {100}, ret = 3, type = 9, epType = 0, leastParam = 1},
	-------------------------type = 5, 统计函数---------------------------------
	{name = "deliveryinterval", argus = {},         ret = 1,    type = 5,   epType = 2,   leastParam = 0},
	{name = "deliveryinterval2", argus = {0},       ret = 1,    type = 5,  epType = 2,   leastParam = 0},
	{name = "deliveryinterval3", argus = {},       ret = 1,    type = 5,  epType = 2,   leastParam = 0},
	{name = "expiredate", argus = {0},         ret = 1,    type = 5,   epType = 3,   leastParam = 1},
	{name = "mainexpiredate", argus = {0},         ret = 1,    type = 5,   epType = 2,   leastParam = 1},
	{name = "standardize",argus ={10,10,10},  	 ret = 0,	 type = 5,   epType = 2,   leastParam = 3},
	{name = "avedev",	argus = {10,10},	 	 ret = 0,	 type = 5,   epType = 2,   leastParam = 2},
	{name = "beta2",	argus = {10,10,10},	     ret = 0,	 type = 5,   epType = 2,   leastParam = 3},
	{name = "relate",	argus ={10,10,10},       ret = 0,	 type = 5,   epType = 2,   leastParam = 3},
	{name = "covar",	argus ={10,10,10}, 	     ret = 0,	 type = 5,   epType = 2,   leastParam = 3},
	{name = "mode",		argus ={0,10},  		 ret = 0,	 type = 5,   epType = 2,   leastParam = 2}, --可以考虑建立另一种层面的优化,以map为cache
	{name = "std",		argus = {10,10},		 ret = 0,	 type = 5,   epType = 2,   leastParam = 2},
	{name = "var",		argus ={10,10},      	 ret = 0,	 type = 5,   epType = 2,   leastParam = 2},
	{name = "varp",		argus ={10,10},        ret = 0,	 type = 5,   epType = 2,   leastParam = 2},
	{name = "stdp",		argus ={10,10},        ret = 0,	 type = 5,   epType = 2,   leastParam = 2},
	{name = "devsq",	argus ={10,10}, 	 	 ret = 0,	 type = 5,   epType = 2,   leastParam = 2},
	{name = "steyx",	argus ={10,10,10},      ret = 0,	 type = 5,   epType = 2,   leastParam = 3},
	{name = "pearson",  argus ={10,10,10},      ret = 0,	 type = 5,   epType = 2,   leastParam = 3},
	{name = "rsq",		argus ={10,10,10},      ret = 0,	 type = 5,   epType = 2,   leastParam = 3},
	{name = "intercept",argus ={10,10,10},  	 ret = 0,	 type = 5,   epType = 2,   leastParam = 3},
	--{name = "harmean",  argus ={10,10}, 	 	 ret = 0,	 type = 5,   epType = 2,   leastParam = 2},
	--{name = "geomean",  argus ={10,10}, 	 	 ret = 0,	 type = 5,   epType = 2,   leastParam = 2},
	{name = "kurt",		argus ={10,10},  		 ret = 0,	 type = 5,   epType = 2,   leastParam = 2},
	{name = "weibull",	argus ={0,0,0,20},    ret = 0,	 type = 5,   epType = 2,   leastParam = 4},
	{name = "binomdist",argus = {0,0,0,20}, 	 ret = 0,	 type = 5,   epType = 2,   leastParam = 4},
	{name = "expondist",argus ={0,0,20}, 	 ret = 0,	 type = 5,   epType = 2,   leastParam = 3},
	{name = "fisher",	argus ={10}, 	 	 ret = 0,	 type = 5,   epType = 2,   leastParam = 1},
	{name = "fisherinv",argus ={10}, 	 	 ret = 0,	 type = 5,   epType = 2,   leastParam = 1},
	{name = "hypgeomdist",argus ={0,0,0,0},  ret = 0,	 type = 5,   epType = 2,   leastParam = 4},
	{name = "negbinomdist",argus ={0,0,0},   ret = 0,	 type = 5,   epType = 2,   leastParam = 3},
	{name = "permut",argus ={0,0},      	 ret = 0,	 type = 5,   epType = 2,   leastParam = 2},
	{name = "poisson",argus ={0,0,0},      	 ret = 0,	 type = 5,   epType = 2,   leastParam = 3},
	{name = "critbinom",argus ={0,0,0}, 	 ret = 0,	 type = 5,   epType = 2,   leastParam = 3},
	{name = "ftest",	argus ={0,0,0}, 	 ret = 0,	 type = 5,   epType = 2,   leastParam = 3},
	{name = "skew",		argus ={0,0},      	 ret = 0,	 type = 5,   epType = 2,   leastParam = 2},
	{name = "small",	argus ={0,0,0},      ret = 0,	 type = 5,   epType = 2,   leastParam = 3},
	{name = "large",	argus ={0,0,0},  	 ret = 0,	 type = 5,   epType = 2,   leastParam = 3},
	{name = "quartile",argus ={0,0,0},      ret = 0,	 type = 5,   epType = 2,   leastParam = 3},
	{name = "trimmean",	argus ={0,0,0},      ret = 0,	 type = 5,   epType = 2,   leastParam = 3},
	{name = "percentile",argus ={0,0,0},     ret = 0,	 type = 5,   epType = 2,   leastParam = 3},
	{name = "percentrank",argus ={0,0,0,0},      ret = 0,	 type = 5,   epType = 2,   leastParam = 4},
	{name = "slope",	argus ={0,0},      ret = 0,	 type = 5,   epType = 2,   leastParam = 2},
	{name = "forcast",	argus ={0,0}, 	 	 ret = 0,	 type = 5,   epType = 2,   leastParam = 2},
	{name = "drl",		argus ={0,0}, 	 	 ret = 0,	 type = 5,   epType = 2,   leastParam = 2},
	--{name = "slope20",	argus ={0,0},      ret = 0,	 type = 5,   epType = 2,   leastParam = 2},
	--{name = "slope21",	argus ={0,0},      ret = 0,	 type = 5,   epType = 2,   leastParam = 2},
	--{name = "slope22",	argus ={0,0},      ret = 0,	 type = 5,   epType = 2,   leastParam = 2},
	{name = "forcast2",	argus ={0,0}, 	 	 ret = 0,	 type = 5,   epType = 2,   leastParam = 2},
	{name = "drl2",		argus ={0,0}, 	 	 ret = 0,	 type = 5,   epType = 2,   leastParam = 2},
	{name = "nolot",	argus ={0,10}, 	 	 ret = 0,	 type = 5,   epType = 2,   leastParam = 2},
	--------------------------to do formula -------------------------------------
	--{name = "finv",		argus ={0,0,0}, 	 ret = 0,	 type = 5,   epType = 2,   leastParam = 3},
	--{name = "beta",		argus = {0},	 	 ret = 0,	 type = 5,   epType = 2,   leastParam = 1},
	--{name = "betadist",	argus = {0,0,0,0,0}, ret = 0,	 type = 5,   epType = 2,   leastParam = 5},
	--{name = "betainf",	argus = {0,0,0,0,0}, ret = 0,	 type = 5,   epType = 2,   leastParam = 5},
	--{name = "chidist",	argus = {0,0}, 	 	 ret = 0,	 type = 5,   epType = 2,   leastParam = 2},
	--{name = "chiinv",	argus = {0,0}, 	 	 ret = 0,	 type = 5,   epType = 2,   leastParam = 2},
	--{name = "confidence",argus ={0,0,0}, 	 ret = 0,	 type = 5,   epType = 2,   leastParam = 3},
	--{name = "fdist",	argus ={0,0,0}, 	 ret = 0,	 type = 5,   epType = 2,   leastParam = 3},
	--{name = "gammadist",argus ={0,0,0,0}, 	 ret = 0,	 type = 5,   epType = 2,   leastParam = 4},
	--{name = "gammainv", argus ={0,0,0}, 	 ret = 0,	 type = 5,   epType = 2,   leastParam = 3},
	--{name = "gammainv", argus ={0}, 	 	 ret = 0,	 type = 5,   epType = 2,   leastParam = 1},
	--{name = "loginv",	argus ={0,0,0},  	 ret = 0,	 type = 5,   epType = 2,   leastParam = 3},
	--{name = "lognormdist",argus ={0,0,0},  	 ret = 0,	 type = 5,   epType = 2,   leastParam = 3},
	--{name = "normdist", argus ={0,0,0,0},    ret = 0,	 type = 5,   epType = 2,   leastParam = 4},
	--{name = "norminv",  argus ={0,0,0},      ret = 0,	 type = 5,   epType = 2,   leastParam = 3},
	--{name = "normsdist",argus ={0},      	 ret = 0,	 type = 5,   epType = 2,   leastParam = 1},
	--{name = "normsinv", argus ={0},      	 ret = 0,	 type = 5,   epType = 2,   leastParam = 1},
	--{name = "tdist",	argus ={0,0,0},      ret = 0,	 type = 5,   epType = 2,   leastParam = 3},
	--{name = "alike",	argus = {1,1,0},	 ret = 0,	 type = 5,   epType = 2,   leastParam = 3},
	--{name = "tinv",		argus ={0,0},      	 ret = 0,	 type = 5,   epType = 2,   leastParam = 2},
	--{name = "ttest",	argus ={1,1,0,0},    ret = 0,	 type = 5,   epType = 2,   leastParam = 4},
	--{name = "ztest",	argus ={1,0,0,0},    ret = 0,	 type = 5,   epType = 2,   leastParam = 4},
	--{name = "prob",     argus ={0,0,0,0,0}, ret = 0,	 type = 5,   epType = 2,   leastParam = 5},

	-------------------------type = 10, 交易系统--------------------------------
	{name = "order", argus = {0,0,0,0},	 ret = 100,	 type = 10,  epType = 2,   leastParam = 2},
	-- {name = "passorder", argus = {0,0,0,0,0,10,0,10,0,0},	 ret = 1,	 type = 10,  epType = 2,   leastParam = 7},
	{name = "passorder", argus = {0,0,0,0,0,0,0,0,0,0,0},	 ret = 100,	 type = 10,  epType = 2,   leastParam = 11},
	--{name = "trade", 	argus = {0,0}, 		 ret = 100,	 type = 10,  epType = 2,   leastParam = 1},
	{name = "hedgestocktrade", 	argus = {0,0}, 		 ret = 100,	 type = 10,  epType = 2,   leastParam = 1},
	{name = "sleep", 	argus = {0}, 		 ret = 100,	 type = 10,  epType = 0,   leastParam = 1},
	{name = "holding",	argus = {0,0,0,0},	 ret = 1,	 type = 10,  epType = 3,   leastParam = 4},
	{name = "holdings", argus = {0}, 		 ret = 1003, type = 10,  epType = 3,   leastParam = 1},
	{name = "ordering",	argus = {0,0,0,0,0},	 ret = 1,	 type = 10,  epType = 3,   leastParam = 4},
	{name = "orderings",	argus = {0,0},	 ret = 1005,	 type = 10,  epType = 3,   leastParam = 1},
	{name = "deal",	argus = {0,0,0,10,0,0},	 ret = 1,	 type = 10,  epType = 3,   leastParam = 4},
	{name = "deals",	argus = {0,0},	 ret = 1004,	 type = 10,  epType = 3,   leastParam = 1},
	{name = "account",	argus = {},		 ret = 3,	 type = 10,	 epType = 3,   leastParam = 0},
	{name = "accounttype",	argus = {},		 ret = 3,	 type = 10,	 epType = 3,   leastParam = 0},
	{name = "taccount",		argus = {0,0},		 ret = 0,	 type = 10,	 epType = 3,   leastParam = 2},
	{name = "cancel",	argus = {0,0,0},		 ret = 100,	 type = 10,  epType = 3,   leastParam = 3},
	--{name = "writeorder", argus = {0,0},		ret = 100,	type = 10,	epType = 2, leastParam = 2},
	{name = "positionadjust", argus = {0,0,0}, ret = 3,   type = 10,  epType = 0, leastParam = 3},
	{name = "marketvalue", argus = {0,0},		ret = 0,	type = 10,	epType = 3, leastParam = 2},
	--{name = "run",		   argus = {},			ret = 100,	type = 10, epType = 0, leastParam = 0},
	{name = "loadbasket",	argus = {0,0,0},	 ret = 1,	 type = 10,  epType = 3,   leastParam = 3},
	{name = "stopprice",	argus = {10,0,0},		ret = 0,	type = 10,	epType = 3, leastParam = 1},
	{name = "contractmultiplier", argus = {0},	     ret = 1,	 type = 10,  epType = 3,   leastParam = 1},
	{name = "dealamounts",	argus = {0,0,10},	 ret = 0,	 type = 10,  epType = 2,   leastParam = 3},
    {name = "algo_passorder", argus = {0,0,0,0,0,0,0,0,0,0,0,0}, ret = 100, type = 10, epType = 2, leastParam = 12},
    {name = "cancel_task", argus = {0,0,0}, ret = 100, type = 10, epType = 3, leastParam = 3},
    {name = "readsignal", argus = {0,0}, ret = 1009, type = 10, epType = 2, leastParam = 2},
    {name = "drawsignal",	argus = {20,10,10}, ret = 100,	 type = 10,   epType = 2,   leastParam = 3},
    {name = "cmdprogress",	argus = {10}, ret = 0,	 type = 10,   epType = 2,   leastParam = 1},
	{name = "cmdstatus",	argus = {10}, ret = 0,	 type = 10,   epType = 2,   leastParam = 1},
	-------------------------type = 11, 指标函数--------------------------------
	{name = "sar",		argus = {0,0,0},	 ret = 0,	 type = 11,  epType = 2,   leastParam = 3},
	{name = "sarturn",	argus = {0,0,0},	 ret = 0,	 type = 11,  epType = 2,   leastParam = 3},
    {name = "getcurrenttrendlinevalue", 	argus = {0,0,0,0,0},	 	ret = 0,	 type = 11,   epType = 3,   leastParam = 5},
	-------------------------type = 12, 动态行情函数--------------------------------
	{name = "condition", argus = {0,0},     ret = 0,	 type = 12,	 epType = 3,   leastParam = 2},
	{name = "dynainfo", argus = {0,0,0},     ret = 0,	 type = 12,	 epType = 3,   leastParam = 1},
	{name = "markettime",	argus = {0},     ret = 0,	 type = 12,   epType = 3,   leastParam = 0},
	{name = "orderdirection",	argus = {},     ret = 1,	 type = 2,   epType = 2,   leastParam = 0},
	--{name = "dbidvol", argus = {0},     ret = 0,	 type = 12,	 epType = 3,   leastParam = 1},
	{name = "blockrank",    argus = {0,0,0},    ret = 0,    type = 12,  epType = 2, leastParam = 3},
	-------------------------type = 13, 系统函数--------------------------------
	{name = "printout", argus = {100},         ret = 100,	 type = 13,	 epType = 2, leastParam = 1},
	{name = "setoutput", argus = {0},         ret = 100,	 type = 13,	 epType = 2, leastParam = 1},
	{name = "md5", argus = {0},         ret = 3,	 type = 13,	 epType = 2, leastParam = 1},
	{name = "crc64", argus = {0,0},         ret = 0,	 type = 13,	 epType = 2, leastParam = 2},
	{name = "crc64num", argus = {0,0},         ret = 0,	 type = 13,	 epType = 2, leastParam = 2},
	{name = "reqid", argus = {},         ret = 3,	 type = 13,	 epType = 2, leastParam = 0},
	{name = "uuid", argus = {},         ret = 1,	 type = 13,	 epType = 2, leastParam = 0},
	{name = "isequalv",  argus = {0,0},		   ret = 2,		 type = 13,  epType = 0, leastParam = 2},
	{name = "isgreater",argus = {0,0},		   ret = 2,		 type = 13,  epType = 0, leastParam = 2},
	{name = "isgreaterequal",  argus = {0,0},  ret = 2,		 type = 13,  epType = 0, leastParam = 2},
	{name = "isless",  argus = {0,0},		   ret = 2,		 type = 13,  epType = 0, leastParam = 2},
	{name = "islessequal",  argus = {0,0},	   ret = 2,		 type = 13,  epType = 0, leastParam = 2},
	{name = "isvalid", argus = {0},			   ret = 2,		 type = 13,  epType = 0, leastParam = 1},
    --{name = "nosorted", argus = {0},			   ret = 2,		 type = 13,  epType = 0, leastParam = 1},
	{name = "setdrtype", argus = {0},		   ret = 100,	 type = 13,	 epType = 0, leastParam = 1},
	{name = "exist1", argus = {0,0},		   ret = 2,	 type = 13,	 epType = 0, leastParam = 2},
	{name = "existrange", argus = {0,0,0},		   ret = 2,	 type = 13,	 epType = 0, leastParam = 3},
	{name = "removekey", argus = {0,0},	   ret = 100,	 type = 13,	 epType = 0, leastParam = 2},
    {name = "holdingornot", argus = {0,0},		   ret = 2,	 type = 13,	 epType = 0, leastParam = 2},
    {name = "tohold", argus = {0,0},	   ret = 100,	 type = 13,	 epType = 0, leastParam = 2},
    {name = "toabandon", argus = {0,0},	   ret = 100,	 type = 13,	 epType = 0, leastParam = 2},
    {name = "multisort", argus ={0,0},     ret = 100,    type = 13,  epType = 0, leastParam = 2},
	{name = "playsound", argus = {20,0,0},  	 ret = 100,	 type = 13,   epType = 2,   leastParam = 3},
    {name = "sendmail",  argus={0,0,0,0,0,0,0},  ret = 2,  type =13,  epType=2,   leastParam = 7},
	{name = "customarg", argus = {0},		    ret = 3,		 type = 13,  epType = 2, leastParam = 1},
    {name = "setcustomarg", argus = {0,0},		    ret = 100,		 type = 13,  epType = 2, leastParam = 2},
	{name = "setshareddata", argus = {0,0},		    ret = 100,		 type = 13,  epType = 2, leastParam = 2},
	{name = "getshareddata", argus = {0},		    ret = 3,		 type = 13,  epType = 2, leastParam = 1},
	{name = "serialize", argus = {0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0},		    ret = 3,		 type = 13,  epType = 2, leastParam = 1},
	{name = "speak", argus = {0,0,0},  	 ret = 100,	 type = 13,   epType = 2,   leastParam = 3},

	-------------------------type = 14, 扩展数据函数--------------------------------
	{name = "extdata", argus = {0,0,0},		ret = 0,	 type = 14,	 epType = 2, leastParam = 1},
	{name = "extdatarange", argus = {0,0,0}, ret = 4,	 type = 14,	 epType = 2, leastParam = 1},
	{name = "extdatamatch", argus = {0,0,0}, ret = 0,	 type = 14,  epType = 2, leastParam = 3},
	{name = "extdatarank",  argus = {0,0,0},	 ret = 0,	 type = 14,	 epType = 2, leastParam = 2},
    {name = "extindexdata",  argus = {0,0,0,10,10},	 ret = 4,	 type = 14,	 epType = 3, leastParam = 5},
    {name = "testrank",     argus = {0,0,0},    ret =0,    type =14,  epType =2,  leastParam =2},
    {name = "extdatabigger", argus = {0,0,0}, ret = 0, type = 14, epType = 2, leastParam = 2},
    {name = "extranktovalue", argus = {0,0,0}, ret = 0, type = 14, epType = 2, leastParam = 3},
    {name = "extranktocode", argus = {0,0,0}, ret = 3, type = 14, epType = 2, leastParam = 3},
    {name = "extblockranktocode", argus = {0,0,0}, ret = 3, type = 14, epType = 2, leastParam = 3},
    {name = "selfcacheptr", argus = {0}, ret = 0, type =14 ,epType = 3, leastParam = 1},
	{name = "extdatablockrank",  argus = {0,0,0},	 ret = 0,	 type = 14,	 epType = 2, leastParam = 3},
    {name = "extdatablocksum",  argus = {0,0},	 ret = 0,	 type = 14,	 epType = 2, leastParam = 2},
    {name = "extdatablocksumrange",  argus = {0,0,0},	 ret = 0,	 type = 14,	 epType = 2, leastParam = 3},
    {name = "extdatablocksplitavg",  argus = {0,0,0,0},	 ret = 0,	 type = 14,	 epType = 2, leastParam = 4},
	-------------------------type = 15, 组合模型函数--------------------------------
	{name = "setgroupmaxholding", argus = {0}, ret = 100,type = 15,  epType = 3,   leastParam = 1},
	{name = "setgroupmode", argus = {0}, ret = 100,	 type = 15,   epType = 3,   leastParam = 1},
	{name = "setgroupindex", argus = {0}, ret = 100, type = 15,	  epType = 3,	leastParam = 1},
	{name = "creategroup", argus = {}, ret = 1007, type = 15,	  epType = 2,	leastParam = 0},
	{name = "groupadd", argus = {0,0}, ret = 1, type = 15,	  epType = 2,	leastParam = 2},
	{name = "groupdel", argus = {0,0}, ret = 1, type = 15,	  epType = 2,	leastParam = 2},
	{name = "groupfind", argus = {0,0}, ret = 1, type = 15,	  epType = 2,	leastParam = 2},
	{name = "groupclear", argus = {0}, ret = 100, type = 15,	  epType = 2,	leastParam = 1},
	{name = "groupcount", argus = {0}, ret = 1, type = 15,	  epType = 2,	leastParam = 1},
	{name = "groupat", argus = {0,0}, ret = 3, type = 15,	  epType = 2,	leastParam = 2},
	{name = "getgroupbuy", argus = {0}, ret = 1007, type = 15,	  epType = 2,	leastParam = 1},
	{name = "getgroupsell", argus = {0}, ret = 1007, type = 15,	  epType = 2,	leastParam = 1},
	{name = "getgroupholdings", argus = {0}, ret = 1007, type = 15,	  epType = 2,	leastParam = 1},
	{name = "groupdebug", argus = {0}, ret = 3, type = 15,	  epType = 2,	leastParam = 1},
	{name = "splitgroupbyextdata", argus = {0,0,0,0,0}, ret = 1007, type = 15,	  epType = 2,	leastParam = 5},
	{name = "splitgroupbyextdatatoprank", argus = {0,0,0,0,0}, ret = 1007, type = 15,	  epType = 2,	leastParam = 5},
	{name = "groupadjust", argus = {0}, ret = 1, type = 15,	  epType = 2,	leastParam = 1},
	{name = "getstocklist", argus = {0}, ret = 1007, type = 15,	  epType = 2,	leastParam = 1},
	{name = "getstocklistadjust", argus = {0}, ret = 1007, type = 15,	  epType = 2,	leastParam = 1},
	{name = "getinitgroup", argus = {}, ret = 1007, type = 15,	  epType = 2,	leastParam = 0},
	-------------------------type = 16, 组合模型运行函数--------------------------------
	{name = "getstockinfo", argus = {0,0}, ret = 1001,	 type = 16,   epType = 2,   leastParam = 1},
	{name = "getstockinfobyindex", argus = {0,0}, ret = 1001,	 type = 16,   epType = 2,   leastParam = 1},
	{name = "isstockinholding", argus = {0}, ret = 2,	 type = 16,   epType = 2,   leastParam = 1},
	{name = "isstockinholdingbyindex", argus = {0}, ret = 2,	 type = 16,   epType = 2,   leastParam = 1},
	{name = "groupbuy", argus = {0}, ret = 100,	 type = 16,   epType = 2,   leastParam = 1},
	{name = "groupbuybyindex", argus = {0}, ret = 100,	 type = 16,   epType = 2,   leastParam = 1},
	{name = "groupsell", argus = {0}, ret = 2,	 type = 16,   epType = 2,   leastParam = 1},
	{name = "groupsellbyindex", argus = {0}, ret = 2,	 type = 16,   epType = 2,   leastParam = 1},
	{name = "grouppossiblebuy", argus = {0}, ret = 100,	 type = 16,   epType = 2,   leastParam = 1},
	{name = "grouppossiblebuybyindex", argus = {0}, ret = 100,	 type = 16,   epType = 2,   leastParam = 1},
	{name = "grouppossiblesell", argus = {0}, ret = 100,	 type = 16,   epType = 2,   leastParam = 1},
	{name = "grouppossiblesellbyindex", argus = {0}, ret = 100,	 type = 16,   epType = 2,   leastParam = 1},
	{name = "getholdinginfo", argus = {0}, ret = 1002, type = 16, epType = 2, leastParam = 1},
	{name = "getholdinginfobyindex", argus = {0}, ret = 1002, type = 16, epType = 2, leastParam = 1},
	{name = "getholdingprofit", argus = {0}, ret = 0,   type = 16, epType = 2, leastParam = 1},
	{name = "getholdingrise", argus = {0}, ret = 0,   type = 16, epType = 2, leastParam = 1},
	{name = "getgrouprise", argus = {0,0}, ret = 0,   type = 16, epType = 2, leastParam = 2},
	{name = "getweight", argus = {0}, ret = 0, type = 16, epType = 0, leastParam = 1},
	{name = "setweight", argus = {0}, ret = 100, type = 16, epType = 0, leastParam = 1},
	{name = "weightsum", argus = {0,0}, ret = 0, type = 16, epType = 2, leastParam = 2},
	{name = "groupdealcount", argus = {}, ret = 0,   type = 16, epType = 2, leastParam = 0},
	{name = "indynamicbasket", argus = {0}, ret = 2,   type = 16, epType = 2, leastParam = 1},
	{name = "checkgroupresult", argus = {},	   ret = 0,	 type = 16,	 epType = 2, leastParam = 0},
	{name = "getstockdatabyname", argus = {0,0,0}, ret = 0,	 type = 16,   epType = 2,   leastParam = 3},
 	{name = "getstockdatabyid", argus = {0,0,0}, ret = 0,	 type = 16,   epType = 2,   leastParam = 3},
 	{name = "setwriteholdings", argus = {0}, ret = 0,	 type = 16,   epType = 2,   leastParam = 1},
		-------------------------type = 0, 金融数据库函数--------------------------------
	--{name = "fdbfinancedata",    argus = {10,10},  ret = 0,	 type = 17,   epType = 2,   leastParam = 2},
	--{name = "fdbfindata",    argus = {0,0,10,10},  ret = 0,	 type = 17,   epType = 2,   leastParam = 4},
	--{name = "fdbldbdatabyst",    argus = {0,0},  ret = 0,	 type = 17,   epType = 2,   leastParam = 2},

	-------------------------type = 0, 投资组合相关函数---------------------------------
	{name = "marketprice", argus = {}, ret = 0,	 type = 0,   epType = 2,   leastParam = 0},
	{name = "marketavgprice", argus = {}, ret = 0,	 type = 0,   epType = 2,   leastParam = 0},
	{name = "algoprice", argus = {}, ret = 0,	 type = 0,   epType = 2,   leastParam = 0},
	{name = "limitprice", argus = {}, ret = 0,	 type = 0,   epType = 2,   leastParam = 0},
	{name = "marketvol", argus = {}, ret = 0,	 type = 0,   epType = 2,   leastParam = 0},
	{name = "algovol", argus = {}, ret = 0,	 type = 0,   epType = 2,   leastParam = 0},
	{name = "initprice", argus = {}, ret = 0,	 type = 0,   epType = 2,   leastParam = 0},
	-------------------------type = 17, 财务数据相关函数---------------------------------
	--{name = "", argus = {}, ret = 0,	 type = 0,   epType = 2,   leastParam = 0},
		-------------------------type = 18, 分级基金数据相关函数---------------------------------
	{name = "strufund",    argus = {0, 0, 10},  ret = 0,	 type = 18,   epType = 2,   leastParam = 1},
	--{name = "gradedfund",  argus = {0, 0, 10},  ret = 0,	 type = 18,   epType = 2,   leastParam = 1},
	
		-------------------------type = 19, Sap相关函数---------------------------------
	--{name = "mongodata",    argus = {0},  ret = 3,	 type = 19,   epType = 2,   leastParam = 1},
	--{name = "mongodata",    argus = {0, 0},  ret = 3,	 type = 19,   epType = 2,   leastParam = 1},
	--{name = "mongodata",    argus = {0, 0, 0},  ret = 3,	 type = 19,   epType = 2,   leastParam = 1},
	--{name = "statistic",    argus = {0, 0, 0, 10},  ret = 1,	 type = 19,   epType = 3,   leastParam = 1},
	--{name = "statistic",    argus = {0, 0, 0, 0, 10},  ret = 1,	 type = 19,   epType = 3,   leastParam = 1},
	--{name = "getmongoids",    argus = {0},  ret = 7,	 type = 19,   epType = 3,   leastParam = 1},
	--{name = "getmongoids",    argus = {0, 0},  ret = 7,	 type = 19,   epType = 3,   leastParam = 1},
	--{name = "mongodatabyid",    argus = {0, 0, 0},  ret = 3,	 type = 19,   epType = 3,   leastParam = 1},
	--{name = "mongodatabyid",    argus = {0, 0, 0, 0},  ret = 3,	 type = 19,   epType = 3,   leastParam = 1},
	--{name = "getdouvector",    argus = {},  ret = 4,	 type = 19,   epType = 0,   leastParam = 0},
	--{name = "getintvector",    argus = {},  ret = 5,	 type = 19,   epType = 0,   leastParam = 0},
	--{name = "getboovector",    argus = {},  ret = 6,	 type = 19,   epType = 0,   leastParam = 0},
	--{name = "getstrvector",    argus = {},  ret = 7,	 type = 19,   epType = 0,   leastParam = 0},
	
	    -------------------------type = 20, 通达信相关函数过后来加的函数-----------------------------------
	{name = "fromopen",     argus={},           ret = 1,    type = 20,   epType = 2,   leastParam = 0},
	{name = "buyvol",       argus={},           ret = 0,    type = 20,   epType = 2,   leastParam = 0},
	{name = "sellvol",        argus={},         ret = 0,    type = 20,   epType = 2,   leastParam = 0},
	{name = "isbuyorder",     argus={},         ret = 1,    type = 20,   epType = 2,   leastParam = 0},
	{name = "issellorder",     argus={},        ret = 1,    type = 20,   epType = 2,   leastParam = 0},
	
	{name = "const", 		argus = {10},    ret = 0,	 type = 20,   epType = 0,   leastParam = 1},
	{name = "zig",	argus = {0,0},	         ret = 0,	 type = 20,  epType = 2,   leastParam = 2},
	{name = "peak",  	argus = {0,0,0},	     ret = 0,	 type = 20,   epType = 2,   leastParam = 2},
	{name = "trough",  	argus = {0,0,0},	     ret = 0,	 type = 20,   epType = 2,   leastParam = 2},
	{name = "upnday",	argus = {10,10},	         ret = 1,	 type = 20,  epType = 1,   leastParam = 2},
	{name = "downnday",	argus = {10,10},	         ret = 1,	 type = 20,  epType = 1,   leastParam = 2},
	{name = "nday",	    argus = {10,10},	         ret = 1,	 type = 20,  epType = 1,   leastParam = 2},
    {name = "turn",     argus = {0,0},      ret = 0,    type = 20,   epType = 2,   leastParam = 1},
	{name = "callpython", 	argus = {0,0,0},	 ret = 0,	 type = 20,   epType = 2,   leastParam = 3},
	{name = "getfindata",    argus = {0,0,0},  ret = 0,	 type = 20,   epType = 2,   leastParam = 2},
	{name = "getfindatabyperiod",    argus = {0,0,10,10,10},  ret = 0,	 type = 20,   epType = 2,   leastParam = 5},
	{name = "getfindatayear",    argus = {0,0},  ret = 0,	 type = 20,   epType = 2,   leastParam = 2},
    {name = "contract",    argus = {0},  ret = 3,	 type = 20,   epType = 2,   leastParam = 1},
	{name = "getlonghubang",    argus = {0,0,0},  ret = 3,	 type = 20,   epType = 2,   leastParam = 3},
	{name = "gettop10shareholder",    argus = {0,0,10},  ret = 3,	 type = 20,   epType = 2,   leastParam = 3},
	{name = "gettop10shareholderbyperiod",    argus = {0,0,0,0,0,0,0},  ret = 3,	 type = 20,   epType = 2,   leastParam = 7},
	{name = "getholdernum",    argus = {0},  ret = 1,	 type = 20,   epType = 2,   leastParam = 1},
	{name = "gettreasury10y",     argus = {0,0},      ret = 0,    type = 20,   epType = 1,   leastParam = 1},
	{name = "finance",            argus = {10},       ret = 0,    type = 20,   epType = 2,   leastParam = 1},
	{name = "tickvoldistribution",    argus = {10,10,10},  ret = 0,	 type = 20,   epType = 2,   leastParam = 3},
	{name = "buysellvols",argus = {10},	     ret = 0,	 type = 20,   epType = 2,   leastParam = 1},
	{name = "transcationstatic",argus = {0,0},     ret = 0,	 type = 20,   epType = 2,   leastParam = 2},
    {name = "transactionstatistic",argus = {0,0},     ret = 0,	 type = 20,   epType = 2,   leastParam = 2},
	{name = "transcationstaticl1",argus = {0,0},     ret = 0,	 type = 20,   epType = 2,   leastParam = 2},
    {name = "transactionstatisticl1",argus = {0,0},     ret = 0,	 type = 20,   epType = 2,   leastParam = 2},
	{name = "northfinancechange",argus = {0},     ret = 0,	 type = 20,   epType = 2,   leastParam = 1},
	{name = "hktstatistics",argus = {0},     ret = 0,	 type = 20,   epType = 2,   leastParam = 1},
	{name = "hktdetails",argus = {0,0},     ret = 0,	 type = 20,   epType = 2,   leastParam = 2},
	{name = "getliangrongtrade",argus = {0,0},     ret = 0,	 type = 20,   epType = 2,   leastParam = 2},
	{name = "gethighlowstat",argus = {0},     ret = 0,	 type = 20,   epType = 2,   leastParam = 1},
	{name = "getquoteaux",argus = {0,0},     ret = 0,	 type = 20,   epType = 2,   leastParam = 2},
	{name = "external_data",argus = {0,0,0,0},     ret = 0,	 type = 20,   epType = 2,   leastParam = 4},
	{name = "iopv",	    argus = {},			 ret = 0,	 type = 20,   epType = 2,   leastParam = 0},
	{name = "get_cb_info",	    argus = {0,0},			 ret = 3,	 type = 20,   epType = 3,   leastParam = 2},
	{name = "get_cb_info_num",	    argus = {0,0},			 ret = 0,	 type = 20,   epType = 3,   leastParam = 2},
	{name = "get_cb_code",	    argus = {0,10},			 ret = 3,	 type = 20,   epType = 3,   leastParam = 1},
	{name = "get_cb_convert_price",	    argus = {0},	 ret = 0,	 type = 20,   epType = 2,   leastParam = 1},
	{name = "getopendate",	    argus = {0},			 ret = 1,	 type = 20,   epType = 3,   leastParam = 1},
	{name = "gethismaincontract",   argus = {0},      ret = 3,    type = 20,  epType = 2,     leastParam = 1},
	{name = "maincontractchange",   argus = {0},      ret = 1,    type = 20,  epType = 2,     leastParam = 1},
	{name = "getrealcontract",   argus = {0},      ret = 3,    type = 20,  epType = 2,     leastParam = 1},
	{name = "getopenamount",	    argus = {},			 ret = 0,	 type = 20,   epType = 3,   leastParam = 0},
	{name = "getopenvol",	    argus = {},			 ret = 0,	 type = 20,   epType = 3,   leastParam = 0},
    {name = "getcapitalflow",	argus = {0, 0},	 ret = 3,	 type = 20,   epType = 2,   leastParam = 2},
    {name = "getcapitalflowbyholder",	argus = {0, 0},	 ret = 0,	 type = 20,   epType = 2,   leastParam = 2},
    {name = "winner", argus = {10}, ret = 0, type = 20, epType = 2, leastParam = 1},
    {name = "cost", argus = {10}, ret = 0, type = 20, epType = 2, leastParam = 1},
    {name = "upstopprice",    argus = {0},  ret = 0,	 type = 20,   epType = 2,   leastParam = 1},
    {name = "downstopprice",    argus = {0},  ret = 0,	 type = 20,   epType = 2,   leastParam = 1},
    {name = "get_etf_statistics",    argus = {0, 0},  ret = 0,	 type = 12,   epType = 2,   leastParam = 2},
    {name = "get_etf_statisticsl2",    argus = {0, 0},  ret = 0,	 type = 12,   epType = 2,   leastParam = 2},
	-------------------------type = 21, 因子数据函数--------------------------------
	{name = "multifactor", argus = {0,0,0},		ret = 0,	 type = 21,	 epType = 2, leastParam = 1},
	{name = "multifactorrank",  argus = {0,0,0},	 ret = 0,	 type = 21,	 epType = 2, leastParam = 1},
	{name = "paramcombcalc",	    argus = {100},	 ret = 1006,	 type = 20,   epType = 0,   leastParam = 1},
	{name = "get_ah_code",	    argus = {0},			 ret = 3,	 type = 20,   epType = 3,   leastParam = 1},
	{name = "getoptinfo",	    argus = {0},	 ret = 1006,	 type = 20,   epType = 2,   leastParam = 1},
	{name = "getoptlistbyundl",	    argus = {0, 0},	 ret = 3,	 type = 20,   epType = 2,   leastParam = 1},
	{name = "getoptcode",	    argus = {0, 0, 0, 0},	 ret = 3,	 type = 20,   epType = 2,   leastParam = 4},
	{name = "getoptundlcode",	    argus = {0},	 ret = 3,	 type = 20,   epType = 2,   leastParam = 1},
	{name = "getoptcodebyno",	    argus = {0, 0, 0, 0, 0, 0, 0, 0},	 ret = 3,	 type = 20,   epType = 2,   leastParam = 8},
	{name = "getoptcodebyno2",	    argus = {0, 0, 0, 0, 0, 0, 0, 0},	 ret = 3,	 type = 20,   epType = 2,   leastParam = 8},
	{name = "getexerciseinterval",	    argus = {0, 0},	 ret = 1,	 type = 20,   epType = 2,   leastParam = 2},
    {name = "getoptiv", argus = {0,0,0},       ret = 0,    type = 20,  epType = 2,   leastParam = 3},
    {name = "getoptivgreek", argus = {0,0,0,0},       ret = 0,    type = 20,  epType = 2,   leastParam = 4},
    {name = "getcbconversionvalue",	    argus = {0},	 ret = 0,	 type = 20,   epType = 2,   leastParam = 1},
    {name = "getcbconversionpremium",	    argus = {0},	 ret = 0,	 type = 20,   epType = 2,   leastParam = 1},
    {name = "getorderflowdetail",	    argus = {0, 0},	 ret = 1008,	 type = 20,   epType = 2,   leastParam = 2},
    {name = "getorderflow",	    argus = {0},	 ret = 1,	 type = 20,   epType = 2,   leastParam = 1},
    {name = "getorderflowunbalance",	    argus = {0, 0, 0},	 ret = 1,	 type = 20,   epType = 2,   leastParam = 3},
    {name = "getorderflowunbalancepricein",	    argus = {0, 0, 0, 0, 0},	 ret = 1,	 type = 20,   epType = 2,   leastParam = 3},
    {name = "getorderflowpoc",	    argus = {},	 ret = 0,	 type = 20,   epType = 2,   leastParam = 0},
    {name = "getorderflowdelta",	    argus = {},	 ret = 1,	 type = 20,   epType = 2,   leastParam = 0},
    {name = "getlastfuturemonth",	    argus = {0, 0},	 ret = 1,	 type = 20,   epType = 2,   leastParam = 2},
    {name = "getlastfuturecode",	    argus = {0, 0},	 ret = 3,	 type = 20,   epType = 2,   leastParam = 2},
    {name = "getspotprodgroup",	    argus = {0},	 ret = 1007,	 type = 20,   epType = 2,   leastParam = 1},
    {name = "getspotprodinst",	    argus = {0,0},	 ret = 3,	 type = 20,   epType = 2,   leastParam = 2},
    {name = "getwarehousereceipt",	    argus = {0,0},	 ret = 0,	 type = 20,   epType = 2,   leastParam = 2},
    {name = "getwarehousename",	    argus = {0,0},	 ret = 3,	 type = 20,   epType = 2,   leastParam = 2},
    {name = "getfutureseats",	    argus = {0,0,0},	 ret = 0,	 type = 20,   epType = 2,   leastParam = 3},
    {name = "getfutureseatsname",	    argus = {0,0,0},	 ret = 3,	 type = 20,   epType = 2,   leastParam = 3},
    {name = "findfutureseats",	    argus = {0,0,0},	 ret = 0,	 type = 20,   epType = 2,   leastParam = 3},
    {name = "productcode",	    argus = {0},	 ret = 3,	 type = 20,   epType = 2,   leastParam = 1},
    {name = "getfuturecode",	    argus = {0},	 ret = 3,	 type = 20,   epType = 2,   leastParam = 1},
    {name = "convindex",	    argus = {0,10},	 ret = 3,	 type = 20,   epType = 2,   leastParam = 2},
    {name = "isdividdate",	    argus = {0},	 ret = 2,	 type = 20,   epType = 2,   leastParam = 1},
    {name = "markettradestatus",	    argus = {0},	 ret = 1,	 type = 20,   epType = 2,   leastParam = 1},
    {name = "dividfactor",	argus = {0}, ret = 0, type = 20,   epType = 2,   leastParam = 1},
    {name = "stocktype",	argus = {0}, ret = 0, type = 20,   epType = 2,   leastParam = 1},
}

index = {
	--{name = "kdj",		argus = {0, 0, 0}, ret = {"k", "d", "j"}, type = 3,    leastParam = 3},
	--{name = "sjyy",		argus = {}, 	   ret = {"close1", "open1", "close2", "up1", "ma5"}, type = 3,  leastParam = 0},
	--{name = "",			argus = {}, ret = {}, type = 0,   leastParam = 0},
}

--[[
	系统预定义证券数据引用类型
--]]
stocktype = {
	{name = "vtopen", 	index = "open"},
	{name = "vthigh", 	index = "high"},
	{name = "vtlow", 	index = "low"},
	{name = "vtclose", 	index = "close"},
	{name = "vtvol", 	index = "vol"},
	{name = "vtbvol", 	index = "bvol"},
	{name = "vtsvol", 	index = "svol"},
	{name = "vtamount", 	index = "amount"},
	{name = "vtopenint", 	index = "openint"},
	{name = "vtsettleprice", index = "settleprice"},
	{name = "vtrise", index = "rise"},
	{name = "vtgclose", index = "gclose"},
	{name = "vtestimatedprice", index = "estimatedprice"},

	{name = "vtadvance", 	index = ""}, --imposible...
	{name = "vtdecline", 	index = ""}, --imposible...

	{name = "iopvopen", 	index = "iopvopen"},
	{name = "iopvo", 		index = "iopvopen"},
	{name = "iopvhigh", 	index = "iopvhigh"},
	{name = "iopvh", 		index = "iopvhigh"},
	{name = "iopvlow", 		index = "iopvlow"},
	{name = "iopvl", 		index = "iopvlow"},
	{name = "iopvclose", 	index = "iopvclose"},
	{name = "iopvc", 		index = "iopvclose"},
}

--[[------------------------------------------------------------
				原始行情数据
--------------------------------------------------------------]]
marketData  = {
	{name = "oopen",	meta = 3001, field = "00"},
	{name = "ohigh",	meta = 3001, field = "01"},
	{name = "olow",		meta = 3001, field = "02"},
	{name = "oclose",	meta = 3001, field = "03"},
	{name = "ovol",		meta = 3001, field = "5"},
	{name = "oamount",	meta = 3001, field = "6"},
	{name = "osettleprice",	meta = 3001, field = "d"},
	{name = "amount",	meta = 3001, field = "6"},
	{name = "indexa",	meta = 3001, field = "6"},
	{name = "close",	meta = 3001, field = "3"},
	{name = "c",		meta = 3001, field = "3"},
	{name = "indexc",	meta = 3001, field = "3"},
	{name = "high",		meta = 3001, field = "1"},
	{name = "h",		meta = 3001, field = "1"},
	{name = "indexh",	meta = 3001, field = "1"},
	{name = "low",		meta = 3001, field = "2"},
	{name = "l",		meta = 3001, field = "2"},
	{name = "indexl",	meta = 3001, field = "2"},
	{name = "open",		meta = 3001, field = "0"},
	{name = "o",		meta = 3001, field = "0"},
	{name = "indexo",	meta = 3001, field = "0"},
	{name = "vol",		meta = 3001, field = "5"},
	{name = "v",		meta = 3001, field = "5"},
	{name = "indexv",	meta = 3001, field = "5"},
	{name = "rise",	    meta = 0, field = "rise"},
	{name = "r",	    meta = 0, field = "rise"},
	{name = "gclose",	    meta = 0, field = "gclose"},
	{name = "settleprice",	meta = 3001, field = "d"},
	{name = "settlement",	meta = 3001, field = "d"},
	{name = "askprice",		meta = 3000, field = "f"},
	{name = "askvol",		meta = 3000, field = "g"},
	{name = "bidprice",		meta = 3000, field = "d"},
	{name = "bidvol",		meta = 3000, field = "e"},
	--{name = "opena",	meta = 3001, field = "a"},
	{name = "openint",	meta = 3001, field = "7"},
	--{name = "openv",	meta = 3001, field = "c"},
	{name = "tickopen",		meta = 3000, field = "8"},
	{name = "ticklast",		meta = 3000, field = "0"},
	{name = "tickhigh",		meta = 3000, field = "9"},
	{name = "ticklow",			meta = 3000, field = "a"},
	{name = "tickamount",		meta = 3000, field = "2"},
	{name = "tickvol",			meta = 3000, field = "1"},
	{name = "ticklastclose",	meta = 3000, field = "c"},
	{name = "tickopenint",		meta = 3000, field = "3"},
	{name = "tickpe",			meta = 3000, field = "6"},
	{name = "transaction", meta = 3000, field = "5"},
	{name = "ticktransaction", meta = 3000, field = "5"},
	{name = "qt",		meta = 3000, field = "qt"}, --parser is using field, but what info is really request, written in formula::registerRequest
	{name = "callstock",    meta = 0, field = ""}, --for ease. tmp. jch
	{name = "marketprice",	meta = 1111, field = "0"},
	{name = "marketavgprice", meta = 1111, field = "1"},
	{name = "algoprice",	meta = 1111, field = "2"},
	{name = "limitprice",	meta = 1111, field = "3"},
	{name = "marketvol",	meta = 1111, field = "4"},
	{name = "algovol",		meta = 1111, field = "5"},
	{name = "initprice",	meta = 1111, field = "6"},
	{name = "bvol",	meta = 4002, field = "1"},
	{name = "svol",	meta = 4002, field = "0"},
	{name = "estimatedprice",	meta = 3000, field = "q"},

	{name = "",			meta = 1001, field = ""},


	{name = "iopvopen",		meta = 4011, field = "0"},
	{name = "iopvhigh",		meta = 4011, field = "1"},
	{name = "iopvlow",		meta = 4011, field = "2"},
	{name = "iopvclose",	meta = 4011, field = "3"},

}

hiddenDataNeed = {
	{name = "sar",		needs = {"high", "low", "close"}},
	{name = "sarturn",	needs = {"high", "low", "close"}},
	{name = "tr",		needs = {"high", "low", "close"}},
	{name = "isdown",	needs = {"open", "close"}},
	{name = "isequal",	needs = {"open", "close"}},
	{name = "isup", 	needs = {"open", "close"}},
	{name = "tickvoldistribution", 	needs = {"close", "askprice","bidprice","vol"}},
}

--[[--------------------------------------------------------------
				界面画线的类型
----------------------------------------------------------------]]
drawtype = {
	{name = "index",		shape = 0 }, --指标线
	{name = "vtsolid",		shape = 0 }, --普通线
	{name = "circledot",	shape = 1 }, --小圆圈线
	{name = "colorstick",	shape = 2 }, --彩色棒状线
	{name = "crossdot",		shape = 3 }, --叉状线
	{name = "linedash",		shape = 4 }, --长虚线
	{name = "linedashdot",	shape = 5 }, --短虚线
	{name = "linedot",		shape = 6 }, --虚线
	{name = "nodraw",		shape = 7 }, --不画该线，可供调试用
	{name = "pointdot",		shape = 8 }, --点状线
	{name = "stick",		shape = 9 }, --棒状线
	{name = "volstick",		shape = 10}, --成交量棒状线
	{name = "main", 		shape = 11}, --主图指标线
	{name = "kline", 		shape = 12}, --K线
	{name = "drawtext",     shape = 13}, --输出文字
	{name = "vtslolid", 	shape = 14}, --垂直线：普通线
	{name = "vtdot", 		shape = 15}, --垂直线：点线
	{name = "vtdashdot", 	shape = 16}, --垂直线：虚线和点交替
	{name = "vtdashdotdot", shape = 17}, --垂直线：虚线和两点交替
	{name = "vtdash",		shape = 18}, --垂直线：虚线
	{name = "vertline",		shape = 19},
	{name = "barset",		shape = 20},
	--{name = "drawarc",		shape = 21},
	{name = "drawbkbmp",	shape = 22},
	{name = "drawbmp",		shape = 23},
	{name = "drawellipse",	shape = 24},
	{name = "drawgbk",		shape = 25},
	{name = "drawicon",		shape = 26},
	{name = "drawline",		shape = 27},
	{name = "drawnumber",	shape = 28},
	{name = "drawrect",		shape = 29},
	{name = "drawsl",		shape = 30},
	--{name = "drawtextex",	shape = 31},
	{name = "explain",		shape = 32},
	{name = "fillrgn",		shape = 33},
	--{name = "partline",		shape = 34},
	{name = "polyline",		shape = 35},
	--{name = "stickline",	shape = 36},
	{name = "indexBool",	shape = 37}, --bool型的指标线
	{name = "noaxis",		shape = 38},
	{name = "nodraw", 		shape = 39},
	{name = "colorstickvol",shape = 40},
	{name = "segmentline", 	shape = 41},
	{name = "stackvolstick",shape = 42},
	{name = "waibushuju"   ,shape = 43},
	{name = "drawband"     ,shape = 45},
	{name = "drawgbk_div"  ,shape = 46},
	{name = "drawstick"  ,      shape = 47},
	{name = "drawarrow"  ,  shape = 48},
	{name = "drawrectangle",shape = 49},

	--{name = "precision0",	shape = 20}, --todo
	{name = "", shape = 0},
}

--[[
	系统预定义颜色
--]]
color = {
	{name = "colorblack",		rgb = 0},
	{name = "colorblue",		rgb = 16711680},
	{name = "colorbrown",		rgb = 16512},
	{name = "colorcyan",		rgb = 16777044},
	{name = "colorgray",		rgb = 8421504},
	{name = "colorgreen",		rgb = 65280},
	{name = "colormagenta",		rgb = 16711935},
	{name = "colorred", 		rgb = 255},
	{name = "colorwhite",		rgb = 16777215},
	{name = "coloryellow",		rgb = 65535},
	{name = "colorblack",		rgb = 0},
	{name = "colorblack",		rgb = 0},
	{name = "colorblack",		rgb = 0},
	{name = "colorblack",		rgb = 0},
	{name = "colorlired",		rgb = 6384127},
	{name = "colorlicyan",		rgb = 16307079},
}


--[[--------------------------------------------------------------
			用户未指定颜色时的系统自定义颜色
----------------------------------------------------------------]]
--[[
defaultColor = {
	0xff0000, 0xff00ff, 0x808080,0x008000, 0x808000, 0x800080, 0xc0c0c0, 0x008080
}
--]]
defaultColor = {
	0xffffff, 0x00ffff, 0xff00ff,0x00ff00, 0xff0000, 0x0080ff, 0xff8000, 0x008000, 0x800080, 0xffff00
}

--[[--------------------------------------------------------------
			提供闭包优化的函数
----------------------------------------------------------------]]
ClosureFormula = {
	{formula = "printout", closure = "c_print", closureIndex = 0,everyPeriod = true},
	{formula = "hhv", closure = "c_hhv", closureIndex = 2,everyPeriod = true},
	{formula = "llv", closure = "c_llv", closureIndex = 2,everyPeriod = true},
	{formula = "ma", closure = "c_ma", closureIndex = 2,everyPeriod = true},
	{formula = "ima", closure = "c_ima", closureIndex = 2,everyPeriod = true},
	{formula = "wma", closure = "c_wma", closureIndex = 2,everyPeriod = true},
	{formula = "sma", closure = "c_sma", closureIndex = 0,everyPeriod = true},
	{formula = "dma", closure = "c_dma", closureIndex = 0,everyPeriod = true},
	{formula = "ema", closure = "c_ema", closureIndex = 0,everyPeriod = true},
	{formula = "tma", closure = "c_tma", closureIndex = 0,everyPeriod = true},
	{formula = "count", closure = "c_count", closureIndex = 2,everyPeriod = true},
	{formula = "cross", closure = "c_cross", closureIndex = 0,everyPeriod = true},
	{formula = "any", closure = "c_any", closureIndex = 2,everyPeriod = true},
	{formula = "exist", closure = "c_exist", closureIndex = 2,everyPeriod = true},
	{formula = "valuewhen", closure = "c_valuewhen", closureIndex = 0,everyPeriod = true},
	{formula = "barslast", closure = "c_barslast", closureIndex = 0,everyPeriod = true},
	{formula = "barslasts", closure = "c_barslasts", closureIndex = 0,everyPeriod = true},
	{formula = "filter", closure = "c_filter", closureIndex = 2,everyPeriod = true},
	{formula = "ref", closure = "c_ref", closureIndex = 2,everyPeriod = true},
    {formula = "inblock", closure = "c_inblock", closureIndex = 2,everyPeriod = true},
    {formula = "inblock2", closure = "c_inblock2", closureIndex = 2,everyPeriod = true},
	{formula = "sum", closure = "c_sum", closureIndex = 2,everyPeriod = true},
	{formula = "longcross", closure = "c_longcross", closureIndex = 3,everyPeriod = true},
	{formula = "all", closure = "c_all", closureIndex = 2,everyPeriod = true},
	{formula = "every", closure = "c_all", closureIndex = 2,everyPeriod = true},
	{formula = "barscount", closure = "c_barscount", closureIndex = 0,everyPeriod = true},
	{formula = "barssince", closure = "c_barssince", closureIndex = 0,everyPeriod = true},
	{formula = "barssincen", closure = "c_barssincen", closureIndex = 0,everyPeriod = true},
	{formula = "beta2", closure = "c_beta2", closureIndex = 2,everyPeriod = true},
	{formula = "hhvbars", closure = "c_hhvbars", closureIndex = 2,everyPeriod = true},
	{formula = "llvbars", closure = "c_llvbars", closureIndex = 2,everyPeriod = true},
	{formula = "last", closure = "c_last", closureIndex = 2,everyPeriod = true},--两个参数
	{formula = "sfilter", closure = "c_sfilter", closureIndex = 0,everyPeriod = true},
	{formula = "tr", closure = "c_tr", closureIndex = 0,everyPeriod = true},
	{formula = "trma", closure = "c_trma", closureIndex = 2,everyPeriod = true},
	{formula = "ret", closure = "c_ret", closureIndex = 2,everyPeriod = true},
	{formula = "hod", closure = "c_hod", closureIndex = 2,everyPeriod = true},
	{formula = "lod", closure = "c_lod", closureIndex = 2,everyPeriod = true},
	{formula = "newhbars", closure = "c_newhbars", closureIndex = 2,everyPeriod = true},
	{formula = "newlbars", closure = "c_newlbars", closureIndex = 2,everyPeriod = true},
	{formula = "sumbars", closure = "c_sumbars", closureIndex = 2,everyPeriod = true},
	{formula = "sar",	closure = "c_sar", closureIndex = 1,everyPeriod = true},
	{formula = "sarturn", closure = "c_sarturn", closureIndex = 2,everyPeriod = true},
	{formula = "order", closure = "c_order", closureIndex = 0,everyPeriod = false},
	-- {formula = "passorder", closure = "c_passorder", closureIndex = 0,everyPeriod = false},
	--{formula = "trade", closure = "c_trade", closureIndex = 0, everyPeriod = false},
	{formula = "hedgestocktrade", closure = "c_hedgestocktrade", closureIndex = 0, everyPeriod = false},
	{formula = "avedev", closure = "c_avedev", closureIndex = 2,everyPeriod = true},
	{formula = "relate", closure = "c_relate", closureIndex = 2,everyPeriod = true},
	{formula = "mode", closure = "c_mode", closureIndex = 2,everyPeriod = true},
	{formula = "covar", closure = "c_covar", closureIndex = 2,everyPeriod = true},
	{formula = "std", closure = "c_std", closureIndex = 2,everyPeriod = true},
	{formula = "var", closure = "c_var", closureIndex = 2,everyPeriod = true},
	{formula = "varp", closure = "c_varp", closureIndex = 2,everyPeriod = true},
	{formula = "stdp", closure = "c_stdp", closureIndex = 2,everyPeriod = true},
	{formula = "devsq", closure = "c_devsq", closureIndex = 2,everyPeriod = true},
	{formula = "trimmean", closure = "c_trimmean", closureIndex = 2,everyPeriod = true},
	{formula = "steyx", closure = "c_steyx", closureIndex = 2,everyPeriod = true},
	{formula = "pearson", closure = "c_pearson", closureIndex = 2,everyPeriod = true},
	{formula = "rsq", closure = "c_rsq", closureIndex = 2,everyPeriod = true},
	{formula = "intercept", closure = "c_intercept", closureIndex = 2,everyPeriod = true},
	--{formula = "harmean", closure = "c_harmean", closureIndex = 2,everyPeriod = true},
	--{formula = "geomean", closure = "c_geomean", closureIndex = 2,everyPeriod = true},
	{formula = "kurt", closure = "c_kurt", closureIndex = 2,everyPeriod = true},
	{formula = "ftest", closure = "c_ftest", closureIndex = 2,everyPeriod = true},
	{formula = "skew", closure = "c_skew", closureIndex = 2,everyPeriod = true},
	{formula = "small", closure = "c_small", closureIndex = 2,everyPeriod = true},
	{formula = "large", closure = "c_large", closureIndex = 2,everyPeriod = true},
	{formula = "quartile", closure = "c_quartile", closureIndex = 2,everyPeriod = true},
	{formula = "percentile", closure = "c_percentile", closureIndex = 2,everyPeriod = true},
	{formula = "median", closure = "c_median", closureIndex = 2,everyPeriod = true},
	{formula = "trimmean", closure = "c_trimmean", closureIndex = 2,everyPeriod = true},
	{formula = "percentrank", closure = "c_percentrank", closureIndex = 2,everyPeriod = true},
	{formula = "slope", closure = "c_slope", closureIndex = 2,everyPeriod = true},
	{formula = "forcast", closure = "c_forecast", closureIndex = 2,everyPeriod = true},
	{formula = "drl", closure = "c_drl", closureIndex = 2,everyPeriod = true},
	--{formula = "slope20", closure = "c_slope20", closureIndex = 2,everyPeriod = true},
	--{formula = "slope21", closure = "c_slope21", closureIndex = 2,everyPeriod = true},
	--{formula = "slope22", closure = "c_slope22", closureIndex = 2,everyPeriod = true},
	{formula = "forcast2", closure = "c_forecast2", closureIndex = 2,everyPeriod = true},
	{formula = "drl2", closure = "c_drl2", closureIndex = 2,everyPeriod = true},
	{formula = "const", closure = "c_const", closureIndex = 0,everyPeriod = false},
	{formula = "issellorder",	closure = "c_issellorder", closureIndex = 1,everyPeriod = true},
    {formula = "isbuyorder",	closure = "c_isbuyorder", closureIndex = 1,everyPeriod = true},
    {formula = "sellvol",	closure = "c_sellvol", closureIndex = 0,everyPeriod = true},
    {formula = "buyvol",	closure = "c_buyvol", closureIndex = 0,everyPeriod = true},
	{formula = "upnday",	closure = "c_upnday", closureIndex = 2,everyPeriod = true},
	{formula = "downnday",	closure = "c_downnday", closureIndex = 2,everyPeriod = true},
	{formula = "nday",	closure = "c_nday", closureIndex = 2,everyPeriod = true},
    {formula = "turn",  closure = "c_turn", closureIndex = 1,everyPeriod = true},
	{formula = "callpython",  closure = "c_callpython", closureIndex = 1,everyPeriod = true},
	{formula = "getfindatayear",  closure = "c_getfindatayear", closureIndex = 1,everyPeriod = true},
	{formula = "getfindata",  closure = "c_getfindata", closureIndex = 1,everyPeriod = true},
	{formula = "getfindatabyperiod",  closure = "c_getfindatabyperiod", closureIndex = 1,everyPeriod = true},
	{formula = "getlonghubang",  closure = "c_get_longhubang", closureIndex = 1,everyPeriod = true},
	{formula = "getholdernum",  closure = "c_get_holderNumber", closureIndex = 1,everyPeriod = true},
	{formula = "gettop10shareholder",  closure = "c_get_top10shareholder", closureIndex = 1,everyPeriod = true},
	{formula = "gettop10shareholderbyperiod",  closure = "c_get_top10shareholderbyperiod", closureIndex = 1,everyPeriod = true},
	{formula = "finance",  closure = "c_finance", closureIndex = 1,everyPeriod = true},
	{formula = "tickvoldistribution",	closure = "c_tickvoldistribution", closureIndex = 0,everyPeriod = true},
	{formula = "buysellvols", closure = "c_buysellvols", closureIndex = 0,everyPeriod = true},
	{formula = "transcationstatic", closure = "c_transcationstatic", closureIndex = 1,everyPeriod = true},
    {formula = "transactionstatistic", closure = "c_transcationstatic", closureIndex = 1,everyPeriod = true},
	{formula = "transcationstaticl1", closure = "c_transcationstaticl1", closureIndex = 1,everyPeriod = true},
    {formula = "transactionstatisticl1", closure = "c_transcationstaticl1", closureIndex = 1,everyPeriod = true},
	{formula = "external_data", closure = "c_external_data", closureIndex = 1,everyPeriod = true},
	{formula = "iopv", closure = "c_iopv", closureIndex = 0,everyPeriod = true},
	{formula = "get_cb_convert_price", closure = "c_get_cb_convert_price", closureIndex = 0,everyPeriod = true},
	{formula = "gethismaincontract", closure = "c_gethismaincontract",closureIndex = 1,everyPeriod = true},
	{formula = "maincontractchange", closure = "c_maincontractchange",closureIndex = 1,everyPeriod = true},
	{formula = "getrealcontract", closure = "c_getrealcontract",closureIndex = 1,everyPeriod = true},
	{formula = "getopenamount", closure = "c_getopenamount",closureIndex = 1,everyPeriod = true},
	{formula = "getopenvol", closure = "c_getopenvol",closureIndex = 1,everyPeriod = true},
	{formula = "blkname", closure = "c_blkname",closureIndex = 0,everyPeriod = true},
	{formula = "findblock", closure = "c_findblock",closureIndex = 0,everyPeriod = true},
	{formula = "orderdirection", closure = "c_orderdirection",closureIndex = 0,everyPeriod = true},
	{formula = "findindex", closure = "c_findindex",closureIndex = 0,everyPeriod = true},
	{formula = "switchindex", closure = "c_switchindex",closureIndex = 0,everyPeriod = true},
	{formula = "extdatablockrank", closure = "c_extdatablockrank",closureIndex = 0,everyPeriod = true},
	{formula = "extdatablocksum", closure = "c_extdatablocksum",closureIndex = 0,everyPeriod = true},
	{formula = "extdatablocksumrange", closure = "c_extdatablocksumrange",closureIndex = 0,everyPeriod = true},
	{formula = "extblockranktocode", closure = "c_extblockranktocode",closureIndex = 0,everyPeriod = true},
	{formula = "blocksize", closure = "c_blocksize",closureIndex = 0,everyPeriod = true},
	{formula = "stockbyblockrank", closure = "c_stockbyblockrank",closureIndex = 0,everyPeriod = true},
	{formula = "blocksum", closure = "c_blocksum",closureIndex = 0,everyPeriod = true},
	{formula = "blockrank", closure = "c_blockrank",closureIndex = 0,everyPeriod = true},
	{formula = "paramcombcalc", closure = "c_paramcombcalc", closureIndex = 0,everyPeriod = true},
	{formula = "serialize", closure = "c_serialize", closureIndex = 0,everyPeriod = true},
	{formula = "getstocklist", closure = "getstocklist", closureIndex = 0,everyPeriod = true},
	{formula = "getinitgroup", closure = "getinitgroup", closureIndex = 0,everyPeriod = true},
	{formula = "getoptinfo", closure = "c_getoptinfo", closureIndex = 0,everyPeriod = true},
	{formula = "getoptlistbyundl", closure = "c_getoptcodebyundl", closureIndex = 0,everyPeriod = true},
	{formula = "getoptcode", closure = "c_getoptcode", closureIndex = 0,everyPeriod = true},
	{formula = "getoptundlcode", closure = "c_getoptundlcode", closureIndex = 0,everyPeriod = true},
	{formula = "getoptcodebyno", closure = "c_getoptcodebyno", closureIndex = 0,everyPeriod = true},
	{formula = "getoptcodebyno2", closure = "c_getoptcodebyno2", closureIndex = 0,everyPeriod = true},
	{formula = "getexerciseinterval", closure = "c_getexerciseinterval", closureIndex = 0,everyPeriod = true},
	{formula = "tdate", closure = "c_tdate", closureIndex = 0,everyPeriod = true},
	{formula = "tweekday", closure = "c_tweekday", closureIndex = 0,everyPeriod = true},
	{formula = "timerat", closure = "c_timerat", closureIndex = 0,everyPeriod = true},
	{formula = "timerafter", closure = "c_timerafter", closureIndex = 0,everyPeriod = true},
	{formula = "deliveryinterval", closure = "c_deliveryinterval", closureIndex = 0,everyPeriod = true},
	{formula = "deliveryinterval2", closure = "c_deliveryinterval2", closureIndex = 0,everyPeriod = true},
	{formula = "deliveryinterval3", closure = "c_deliveryinterval3", closureIndex = 0,everyPeriod = true},
	{formula = "getcbconversionvalue", closure = "c_getcbconversionvalue", closureIndex = 0,everyPeriod = true},
	{formula = "getcbconversionpremium", closure = "c_getcbconversionpremium", closureIndex = 0,everyPeriod = true},
	{formula = "getorderflowdetail", closure = "c_getorderflowdetail", closureIndex = 0,everyPeriod = true},
	{formula = "getorderflow", closure = "c_getorderflow", closureIndex = 0,everyPeriod = true},
	{formula = "getorderflowunbalance", closure = "c_getorderflowunbalance", closureIndex = 0,everyPeriod = true},
	{formula = "getorderflowunbalancepricein", closure = "c_getorderflowunbalancepricein", closureIndex = 0,everyPeriod = true},
	{formula = "getorderflowpoc", closure = "c_getorderflowpoc", closureIndex = 0,everyPeriod = true},
	{formula = "getorderflowdelta", closure = "c_getorderflowdelta", closureIndex = 0,everyPeriod = true},
	{formula = "getlastfuturemonth", closure = "c_getlastfuturemonth", closureIndex = 0,everyPeriod = true},
	{formula = "getlastfuturecode", closure = "c_getlastfuturecode", closureIndex = 0,everyPeriod = true},
	{formula = "extdatablocksplitavg", closure = "c_extdatablocksplitavg", closureIndex = 0,everyPeriod = true},
	{formula = "getcapitalflow", closure = "c_getcapitalflow", closureIndex = 0,everyPeriod = true},
	{formula = "getcapitalflowbyholder", closure = "c_getcapitalflowbyholder", closureIndex = 0,everyPeriod = true},
	{formula = "getspotprodgroup", closure = "c_getspotprodgroup", closureIndex = 0,everyPeriod = true},
	{formula = "getspotprodinst", closure = "c_getspotprodinst", closureIndex = 0,everyPeriod = true},
	{formula = "getwarehousereceipt", closure = "c_getwarehousereceipt", closureIndex = 0,everyPeriod = true},
	{formula = "getwarehousename", closure = "c_getwarehousename", closureIndex = 0,everyPeriod = true},
	{formula = "getfutureseats", closure = "c_getfutureseats", closureIndex = 0,everyPeriod = true},
	{formula = "getfutureseatsname", closure = "c_getfutureseatsname", closureIndex = 0,everyPeriod = true},
	{formula = "findfutureseats", closure = "c_findfutureseats", closureIndex = 0,everyPeriod = true},
	{formula = "getfuturecode", closure = "c_getfuturecode", closureIndex = 0,everyPeriod = true},
    {formula = "winner", closure = "c_winner", closureIndex = 1, everyPeriod = true},
    {formula = "cost", closure = "c_cost", closureIndex = 1, everyPeriod = true},
    {formula = "findblocklist", closure = "c_findblocklist", closureIndex = 0, everyPeriod = true},
    {formula = "unitofquantity", closure = "c_unitofquantity", closureIndex = 0, everyPeriod = true},
    {formula = "equalweightindex", closure = "c_equalweightindex", closureIndex = 0, everyPeriod = true},
    {formula = "isindexorglr", closure = "c_isindexorglr", closureIndex = 0, everyPeriod = true},
    {formula = "isetfcode", closure = "c_isetfcode", closureIndex = 0, everyPeriod = true},
    {formula = "isindexcode", closure = "c_isindexcode", closureIndex = 0, everyPeriod = true},
    {formula = "isfuturecode", closure = "c_isfuturecode", closureIndex = 0, everyPeriod = true},
    {formula = "upstopprice",  closure = "c_upstopprice", closureIndex = 1,everyPeriod = true},
    {formula = "downstopprice",  closure = "c_downstopprice", closureIndex = 1,everyPeriod = true},
    {formula = "barslastcount", closure = "c_barslastcount", closureIndex = 0,everyPeriod = true},
    {formula = "dividfactor", closure = "c_dividfactor", closureIndex = 0,everyPeriod = true},
    {formula = "readsignal", closure = "c_readsignal", closureIndex = 0, everyPeriod = true},
    {formula = "drawsignal", closure = "c_drawsignal", closureIndex = 0, everyPeriod = true},
    {formula = "cmdprogress", closure = "c_cmdprogress", closureIndex = 0, everyPeriod = true},
    {formula = "cmdstatus", closure = "c_cmdstatus", closureIndex = 0, everyPeriod = true},
    {formula = "stocktype", closure = "c_stocktype", closureIndex = 0, everyPeriod = true},
    {formula = "convindex", closure = "c_convindex", closureIndex = 0, everyPeriod = true},
    {formula = "mema", closure = "c_mema", closureIndex = 0,everyPeriod = true},
    {formula = "getinstrumentdetail", closure = "c_getinstrumentdetail", closureIndex = 0,everyPeriod = true},
    {formula = "limitupperformance", closure = "c_limitupperformance", closureIndex = 0,everyPeriod = true},
    {formula = "fundnetvalue", closure = "c_fundnetvalue", closureIndex = 0,everyPeriod = true},
    {formula = "get_etf_statistics", closure = "c_get_etf_statistics", closureIndex = 0,everyPeriod = true},
    {formula = "get_etf_statisticsl2", closure = "c_get_etf_statisticsl2", closureIndex = 0,everyPeriod = true},
}

ClosureFormula2 = {
    {formula = "callstock2", closure = "callstock2", closureIndex = 0,everyPeriod = true},
}

--[[--------------------------------------------------------------
			      股票代码的匹配的正则表达式集合
----------------------------------------------------------------]]
MarketRegex = {
	{market = "IF", regex = "(((if|ih|ic|tf|ts|t)([0,1,l,L][0-9]))|((IF|IH|IC|TF|TS|T)([0,1,l,L][0-9])))"},
	{market = "SH", regex = "sh(((60|90|50|58)[0-9]{4})|[0-9]{6}|000300[0-1]{2})"},
	{market = "SH", regex = "SH(((60|90|50|58)[0-9]{4})|([0-9]{6})|000300[0-1]{2})"},
	{market = "SHO", regex = "sho(1000[0-9]{4})"},
	{market = "SHO", regex = "SHO(1000[0-9]{4})"},
	{market = "SZ", regex = "sz(((00|30|20|39|03|18|16|15)[0-9]{4})|([0-9]{6}))"},
	{market = "SZ", regex = "SZ(((00|30|20|39|03|18|16|15)[0-9]{4})|([0-9]{6}))"},
	{market = "SH", regex = "204[0-9]{3}"},
	{market = "SZ", regex = "(((00|30|20|39|03|18|16|15)[0-9]{4})|([0-9]{6}))"},
	{market = "SH", regex = "(((60|90|50|58)[0-9]{4})|([0-9]{6})|000300[0-1]{2})"},
	{market = "SF", regex = "(((CU|AL|RU|FU|ZN|AU|RB|WR|BU|PB|AG|SN|NI|HC|SC|SP)[0-9]{2,4})|H30009.CSI)"},
	{market = "SF", regex = "(((cu|al|ru|fu|zn|au|rb|wr|bu|pb|ag|sn|ni|hc|sc|SP)[0-9]{2,4})|h30009.csi)"},
	{market = "DF", regex = "((a|b|m|c|y|l|v|p|i|bb|fb|pp|cs|j|jd|jm|eg)[0-9]{2,4})"},
	{market = "DF", regex = "((A|B|M|C|Y|L|V|P|I|BB|FB|PP|CS|J|JD|JM|EG)[0-9]{2,4})"},
	{market = "ZF", regex = "((AP|CF|CJ|CY|FG|JR|LR|MA|OI|PM|RI|RM|RS|SF|SM|SR|TA|WH|ZC)[0-9]{2,3})"},
	{market = "ZF", regex = "((ap|cf|cj|cy|fg|jr|lr|ma|oi|pm|ri|rm|rs|sf|sm|sr|ta|wh|zc)[0-9]{2,3})"},
	{market = "OF", regex = "OF(([0-9]{6})|(XT[0-9]{6}))"},
	{market = "OF", regex = "of(([0-9]{6})|(XT[0-9]{6}))"},
    {market = "ED", regex = "wd([0-9]{6}.(OF))"},
    {market = "ED", regex = "WD([0-9]{6}.(OF))"},
    {market = "NASDAQ", regex = "NASDAQ([a-z\\-0-9A-Z_]{1,15})"},
    {market = "NASDAQ", regex = "nasdaq([a-z\\-0-9A-Z_]{1,15})"},
    {market = "NYSE", regex = "NYSE([a-z\\-0-9A-Z_]{1,15})"},
    {market = "NYSE", regex = "nyse([a-z\\-0-9A-Z_]{1,15})"},
	{market = "SEHK", regex = "SEHK((0[0-9]{4}|84602)|([A-Z]{3,6}[0-9]{0,3}))"},
    {market = "SEHK", regex = "sehk((0[0-9]{4}|84602)|([A-Z]{3,6}[0-9]{0,3}))"},
	{market = "YSWP", regex = "yswp(cn|cn[0-9]{4}|cnindex)"},
    {market = "YSWP", regex = "YSWP(CN|CN[0-9]{4}|CNINDEX)"},
	{market = "", regex = ""},
}

--[[--------------------------------------------------------------
			      交易所的基本信息
				  cnname	:	交易所中文名称
----------------------------------------------------------------]]
MarketBasicInfo = {
	{market = "IF", opentime = {093000, 130000}, closetime = {113000, 150000}, mindiff = 0.2, volunit = 1, cnname = "中国金融期货交易所"},
	{market = "SH", opentime = {093000, 130000}, closetime = {113000, 150000}, mindiff = 0.01, volunit = 100, cnname = "上海证券交易所"},
	{market = "SZ", opentime = {093000, 130000}, closetime = {113000, 150000}, mindiff = 0.01, volunit = 100, cnname = "深圳证券交易所"},
	{market = "SHO", opentime = {093000, 130000}, closetime = {113000, 150000}, mindiff = 0.01, volunit = 100, cnname = "上海证券交易所期权"},
	{market = "HGT", opentime = {090000, 130000}, closetime = {120000, 160000}, mindiff = 0.01, volunit = 100, cnname = "沪港通"},
	{market = "SGT", opentime = {090000, 130000}, closetime = {120000, 160000}, mindiff = 0.01, volunit = 100, cnname = "深港通"},
	{market = "NEEQ", opentime = {093000, 130000}, closetime = {113000, 150000}, mindiff = 0.01, volunit = 100, cnname = "新三板"},
    {market = "SF", opentime = {090000, 103000, 133000}, closetime = {101500, 113000, 150000}, nightopentime = {210000, 000000}, nightclosetime = {235959, 023000}, mindiff = 0.1, volunit = 100, cnname = "上海期货交易所"},
    {market = "DF", opentime = {090000, 103000, 133000}, closetime = {101500, 113000, 150000}, nightopentime = {210000}, nightclosetime = {233000}, mindiff = 0.1, volunit = 100, cnname = "大连期货交易所"},
    {market = "ZF", opentime = {090000, 103000, 133000}, closetime = {101500, 113000, 150000}, nightopentime = {210000}, nightclosetime = {233000}, mindiff = 0.1, volunit = 100, cnname = "郑州期货交易所"},
	{market = "YSWP", opentime = {090000}, closetime = {155500}, nightopentime = {164000,000000}, nightclosetime = {235959, 020000}, indexopentime = {093000, 130000}, indexclosetime = {113000, 150000}, mindiff = 0.1, volunit = 100, cnname = "易盛外盘商品期货"},
	{market = "INE", opentime = {090000, 103000, 133000}, closetime = {101500, 113000, 150000}, nightopentime = {210000, 000000}, nightclosetime = {235959, 023000}, mindiff = 0.01, volunit = 100, cnname = "能源中心"},
}

MarketNameConfig = {
	{market = "CFFEX",  cnname = "中国金融期货交易所(CFFEX)"},
	{market = "CZCE", cnname = "郑州期货交易所(CZCE)"},
	{market = "DCE", cnname = "大连商品交易所(DCE)"},
	{market = "SH", cnname = "上海证券交易所(SSE)"},
	{market = "SHFE", cnname = "上海期货交易所(SHFE)"},
	{market = "SZ", cnname = "深圳证劵交易所(SZSE)"},
    {market = "OF", cnname = "开放式基金(OEF)"},
    {market = "SHO", cnname = "上海证券交易所期权(SHO)"},
    {market = "SZO", cnname = "深圳证劵交易所期权(SZO)"},
    {market = "HGT", cnname = "沪港通(HGT)"},
    {market = "SGT", cnname = "深港通(SGT)"},
    {market = "INE", cnname = "能源中心(INE)"},
    {market = "HK", cnname = "香港联合交易所(HK)"},
    {market = "BKZS", cnname = "板块指数(BKZS)"},
    {market = "GI", cnname = "全球市场(GI)"},
    {market = "LSE", cnname = "伦敦证券交易所(LSE)"},
	{market = "BJ", cnname = "北京证劵交易所(BJ)"},
	{market = "GF", cnname = "广州期货交易所(GFEX)"},
}
--[[--------------------------------------------------------------
			      内部的变量结构
				  keyword	:	param的名称
				  items		: 	param的具体内容及其默认值
----------------------------------------------------------------]]
LuaStructs = {
	{keyword = "tradeparam", items = {
		{name="ac", isString=true, default=""}, --//账号组名
		{name="potype", isString=false, default=0}, --//分单模式
		{name="market", isString=true, default=""}, --//市场
		{name="stcode", isString=true, default=""}, --//股票代码
		{name="optype", isString=false, default=0}, --//下单方向
		{name="ortype", isString=false, default=0}, --//算法交易类型
		{name="prtype", isString=false, default=0}, --//报价方式
		{name="suprice", isString=false, default=0}, --//单笔超价
		{name="sptype", isString=false, default=0}, --//单笔超价类型
		{name="fiprice", isString=false, default=0}, --//基准价格
		{name="vol", isString=false, default=0}, --//交易总量
		{name="svtype", isString=false, default=0}, --//单笔基准量
		{name="svrate", isString=false, default=0}, --//基准量比例
		{name="orinterval", isString=false, default=0}, --//下单间隔
		{name="delinterval", isString=false, default=0}, --//撤单间隔
		{name="lvmin", isString=false, default=0}, --//尾单最小量
		{name="lvmtype", isString=false, default=0}, --//尾单最小量类型，缺省比例
		{name="spenable", isString=false, default=0}, --//超价启用笔数
		{name="prange", isString=false, default=0}, --//波动区间
		{name="prtype", isString=false, default=0}, --//波动区间类型
		{name="svmax", isString=false, default=0}, --//单笔最大量
		{name="vatime", isString=false, default=0}, --//有效时间，两种格式比如164820,
		{name="mocount", isString=false, default=0}, --//最大委托次数
		{name="usernum", isString=true, default="00"}, --//用户自定义的下单编号
	},},
	{keyword = "childresult", items = {
		{name = "sell", isString = false, default = 0},
		{name = "buy", isString = false, default = 0},
		{name = "price", isString = false, default = 0},
		{name = "holding", isString = false, default = 0},
        {name = "suspend", isString = false, default = 0},
        {name = "oprice", isString = false, default = 0},
        {name = "ocloseprice", isString = false, default = 0},
	},},
	{keyword = "stockholdinginfo", items = {
		{name = "holding", isString = false, default = 0},
		{name = "buyprice", isString = false, default = 0},
		{name = "buydate", isString = false, default = 0},
		{name = "profit", isString = false, default = 0},
		{name = "price", isString = false, default = 0},
		{name = "holdingperiods", isString = false, default = 0},
		{name = "profitrank", isString = false, default = 0},
		{name = "position", isString = false, default = 0},
	},},
	{keyword = "positiondetail", items = {
		{name = "exchangeid", isString = true, default = ""},
		{name = "exchangename", isString = true, default = ""},
		{name = "productid", isString = true, default = ""},
		{name = "productname", isString = true, default = ""},
		{name = "instrumentid", isString = true, default = ""},
		{name = "instrumentname", isString = true, default = ""},
		{name = "hedgeflag", isString = false, default = 0},
		{name = "direction", isString = false, default = 0},
		{name = "opendate", isString = true, default = ""},
		{name = "tradeid", isString = true, default = ""},
		{name = "volume", isString = false, default = 0},
		{name = "openprice", isString = false, default = 0},
		{name = "tradingday", isString = true, default = ""},
		{name = "margin", isString = false, default = 0},
		{name = "opencost", isString = false, default = 0},
		{name = "settlementprice", isString = false, default = 0},
		{name = "closevolume", isString = false, default = 0},
		{name = "closeamount", isString = false, default = 0},
		{name = "dloatprofit", isString = false, default = 0},
		{name = "closeprofit", isString = false, default = 0},
		{name = "marketvalue", isString = false, default = 0},
		{name = "positioncost", isString = false, default = 0},
		{name = "positionprofit", isString = false, default = 0},
		{name = "lastsettlementprice", isString = false, default = 0},
		{name = "instrumentvalue", isString = false, default = 0},
		{name = "istoday", isString = false, default = 0},
		{name = "xttag", isString = false, default = 0},
		{name = "stockholder", isString = true, default = ""},
		{name = "frozenvolume", isString = false, default = 0},
		{name = "canusevolume", isString = false, default = 0},
		{name = "onroadvolume", isString = false, default = 0},
		{name = "yesterdayvolume", isString = false, default = 0},
		{name = "lastprice", isString = false, default = 0},
		{name = "profitrate", isString = false, default = 0},
		{name = "futuretradetype", isString = false, default = 0},
		{name = "expiredate", isString = true, default = ""},
		{name = "comtradeid", isString = true, default = ""},
		{name = "legid", isString = false, default = 0},
		{name = "totalcost", isString = false, default = 0},
		{name = "singlecost", isString = false, default = 0},
		{name = "coveredvolume", isString = false, default = 0},
		{name = "sideflag", isString = true, default = ""},
		{name = "referencerate", isString = false, default = 0},
		{name = "structfundvol", isString = false, default = 0},
		{name = "redemptionvolume", isString = false, default = 0},
		{name = "prenablevoume", isString = false, default = 0},
		{name = "realusedmargin", isString = false, default = 0},
		{name = "royalty", isString = false, default = 0},
	},},
		{keyword = "orderdetail", items = {
		{name = "exchangeid", isString = true, default = ""},
		{name = "exchangename", isString = true, default = ""},
		{name = "productid", isString = true, default = ""},
		{name = "productname", isString = true, default = ""},
		{name = "instrumentid", isString = true, default = ""},
		{name = "instrumentname", isString = true, default = ""},
		{name = "sessionid", isString = false, default = 0},
		{name = "frontid", isString = false, default = 0},
		{name = "orderref", isString = true, default = ""},
		{name = "orderpricetype", isString = false, default = 0},
		{name = "direction", isString = false, default = 0},
		{name = "offsetflag", isString = false, default = 0},
		{name = "hedgeflag", isString = false, default = 0},
		{name = "limitprice", isString = false, default = 0},
		{name = "volumetotaloriginal", isString = false, default = 0},
		{name = "ordersubmitstatus", isString = false, default = 0},
		{name = "ordersysid", isString = true, default = ""},
		{name = "orderstatus", isString = false, default = 0},
		{name = "volumetraded", isString = false, default = 0},
		{name = "volumetotal", isString = false, default = 0},
		{name = "errorid", isString = false, default = 0},
		{name = "errormsg", isString = true, default = ""},
		{name = "taskid", isString = false, default = 0},
		{name = "frozenmargin", isString = false, default = 0},
		{name = "frozencommission", isString = false, default = 0},
		{name = "insertdate", isString = true, default = ""},
		{name = "inserttime", isString = true, default = ""},
		{name = "xttag", isString = false, default = 0},
		{name = "tradeprice", isString = false, default = 0},
		{name = "cancelamount", isString = false, default = 0},
		{name = "optname", isString = true, default = ""},
		{name = "tradeamount", isString = false, default = 0},
		{name = "entrusttype", isString = false, default = 0},
		{name = "cancelinfo", isString = true, default = ""},
		{name = "undercode", isString = true, default = ""},
		{name = "covereflag", isString = false, default = 0},
		{name = "orderpricermb", isString = false, default = 0},
		{name = "tradeamountrmb", isString = false, default = 0},
		{name = "referencerate", isString = false, default = 0},
		{name = "compactno", isString = true, default = ""},
		{name = "cashgroupprop", isString = false, default = 0},
		{name = "shortoccupedmargin", isString = false, default = 0},
		{name = "xttrade", isString = true, default = ""},
	},},
		{keyword = "dealdetail", items = {
		{name = "exchangeid", isString = true, default = ""},
		{name = "exchangename", isString = true, default = ""},
		{name = "productid", isString = true, default = ""},
		{name = "productname", isString = true, default = ""},
		{name = "instrumentid", isString = true, default = ""},
		{name = "instrumentname", isString = true, default = ""},
		{name = "tradeid", isString = true, default = ""},
		{name = "orderref", isString = true, default = ""},
		{name = "ordersysid", isString = true, default = ""},
		{name = "direction", isString = false, default = 0},
		{name = "offsetflag", isString = false, default = 0},
		{name = "hedgeflag", isString = false, default = 0},
		{name = "price", isString = false, default = 0},
		{name = "volume", isString = false, default = 0},
		{name = "tradedate", isString = true, default = ""},
		{name = "tradetime", isString = true, default = ""},
		{name = "comssion", isString = false, default = 0},
		{name = "tradeamount", isString = false, default = 0},
		{name = "taskid", isString = false, default = 0},
		{name = "xttag", isString = false, default = 0},
		{name = "orderpricetype", isString = false, default = 0},
		{name = "optname", isString = true, default = ""},
		{name = "entrusttype", isString = false, default = 0},
		{name = "futuretradetype", isString = false, default = 0},
		{name = "realoffsetflag", isString = false, default = 0},
		{name = "coveredflag", isString = false, default = 0},
		{name = "closetodayvolume", isString = false, default = 0},
		{name = "orderpricermb", isString = false, default = 0},
		{name = "pricermb", isString = false, default = 0},
		{name = "tradeamountrmb", isString = false, default = 0},
		{name = "referencerate", isString = false, default = 0},
		{name = "xttrade", isString = false, default = 0},
		{name = "compactno", isString = true, default = ""},
		{name = "closeprofit", isString = false, default = 0},
	},},
		{keyword = "paramResult", items = {
		{name = "zh", isString = true, default = ""},
		{name = "result", isString = false, default = 0},
		{name = "total", isString = false, default = 0},
		{name = "conds", isString = false, default = 0},
		{name = "undlcode", isString = true, default = ""},
		{name = "exerciseprice", isString = false, default = 0},
		{name = "side", isString = true, default = ""},
		{name = "delivdate", isString = false, default = 0},
		{name = "exercisedate", isString = false, default = 0},
		{name = "contracttype", isString = false, default = 0},
	},},
		{keyword = "priceVolumeData", items = {
		{name = "time", isString = true, default = ""},
		{name = "size", isString = false, default = 0},
		{name = "index", isString = false, default = 0},
		{name = "price", isString = false, default = 0},
		{name = "buynum", isString = false, default = 0},
		{name = "sellnum", isString = false, default = 0},
	},},
		{keyword = "ordersignal", items = {
		{name = "timetag", isString = false, default = 0},
		{name = "time", isString = true, default = ""},
		{name = "date", isString = true, default = ""},
		{name = "stockcode", isString = true, default = ""},
		{name = "operation", isString = true, default = ""},
		{name = "price", isString = false, default = 0},
		{name = "volume", isString = false, default = 0},
	},},
}
