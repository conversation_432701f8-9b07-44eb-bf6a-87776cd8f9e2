# 金融市场数据服务 API 文档

## 概述

金融市场数据服务提供股票成交信息、K线数据等金融市场数据的API接口。

- **版本**: 1.0.0
- **基础URL**: `http://localhost:8000`
- **API前缀**: `/api/v1`

## 认证

当前版本不需要认证。

## 响应格式

所有API响应都使用统一的JSON格式：

```json
{
    "code": 200,
    "message": "成功",
    "data": {},
    "timestamp": "2024-01-01T12:00:00"
}
```

- `code`: 状态码（200表示成功）
- `message`: 响应消息
- `data`: 响应数据
- `timestamp`: 响应时间戳

## 错误码

| 状态码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 404 | 资源未找到 |
| 422 | 参数验证失败 |
| 500 | 服务器内部错误 |
| 503 | 服务不可用 |

## API 接口

### 1. 健康检查

#### GET /
根路径健康检查

**响应示例**:
```json
{
    "code": 200,
    "message": "金融市场数据服务运行正常",
    "data": {
        "service": "market-data-api",
        "version": "1.0.0",
        "status": "running"
    }
}
```

#### GET /health
详细健康检查

### 2. 股票成交信息

#### GET /api/v1/market/tick/{symbol}
获取单个股票的成交信息

**参数**:
- `symbol` (path): 股票代码，如 "000001"
- `exchange` (query): 交易所代码，如 "SZ"

**响应示例**:
```json
{
    "code": 200,
    "message": "获取股票成交信息成功",
    "data": {
        "symbol": "000001",
        "exchange": "SZ",
        "name": "平安银行",
        "datetime": "2024-01-01T14:30:00",
        "timestamp": 1704096600000,
        "last_price": 10.50,
        "open": 10.30,
        "high": 10.60,
        "low": 10.20,
        "pre_close": 10.40,
        "volume": 1000000,
        "amount": 10500000,
        "bid_price_1": 10.49,
        "ask_price_1": 10.51,
        "bid_volume_1": 1000,
        "ask_volume_1": 2000,
        "change": 0.10,
        "change_percent": 0.96
    }
}
```

#### POST /api/v1/market/tick/multiple
获取多个股票的成交信息

**请求体**:
```json
{
    "symbols": ["000001", "000002"],
    "exchanges": ["SZ", "SZ"]
}
```

#### GET /api/v1/market/tick
获取所有股票的成交信息

### 3. K线数据

#### GET /api/v1/market/kline/{symbol}
获取单个股票的K线信息

**参数**:
- `symbol` (path): 股票代码
- `exchange` (query): 交易所代码
- `interval` (query): K线周期，默认"1m"
  - 支持: 1m, 5m, 15m, 30m, 1h, 4h, 1d, 1w, 1M
- `start_time` (query): 开始时间（可选）
- `end_time` (query): 结束时间（可选）
- `limit` (query): 数据条数限制，默认100，最大1000

**响应示例**:
```json
{
    "code": 200,
    "message": "获取股票K线信息成功，共 10 条数据",
    "data": [
        {
            "symbol": "000001",
            "exchange": "SZ",
            "datetime": "2024-01-01T14:30:00",
            "timestamp": 1704096600000,
            "interval": "1m",
            "open": 10.30,
            "high": 10.35,
            "low": 10.28,
            "close": 10.32,
            "pre_close": 10.30,
            "volume": 50000,
            "amount": 515000,
            "change": 0.02,
            "change_percent": 0.19
        }
    ]
}
```

#### POST /api/v1/market/kline/multiple
获取多个股票的K线信息

#### GET /api/v1/market/kline
获取所有股票的K线信息

### 4. 实时数据订阅

#### GET /api/v1/market/subscription/status
获取订阅服务状态

**响应示例**:
```json
{
    "code": 200,
    "message": "订阅服务状态获取成功",
    "data": {
        "enabled": true,
        "running": true,
        "subscribed_symbols": ["000001.SZ", "000002.SZ"],
        "subscribed_count": 2
    }
}
```

#### GET /api/v1/market/subscription/data
获取订阅的实时数据

#### POST /api/v1/market/subscription/subscribe
订阅股票实时数据

**参数**:
- `symbol` (query): 股票代码
- `exchange` (query): 交易所代码

#### DELETE /api/v1/market/subscription/unsubscribe
取消订阅股票实时数据

## 使用示例

### Python 示例

```python
import requests

# 获取单个股票成交信息
response = requests.get("http://localhost:8000/api/v1/market/tick/000001?exchange=SZ")
data = response.json()
print(data)

# 获取K线数据
response = requests.get("http://localhost:8000/api/v1/market/kline/000001?exchange=SZ&interval=1m&limit=10")
data = response.json()
print(data)

# 批量获取股票信息
payload = {
    "symbols": ["000001", "000002", "600000"],
    "exchanges": ["SZ", "SZ", "SH"]
}
response = requests.post("http://localhost:8000/api/v1/market/tick/multiple", json=payload)
data = response.json()
print(data)
```

### JavaScript 示例

```javascript
// 获取单个股票成交信息
fetch('http://localhost:8000/api/v1/market/tick/000001?exchange=SZ')
    .then(response => response.json())
    .then(data => console.log(data));

// 批量获取股票信息
fetch('http://localhost:8000/api/v1/market/tick/multiple', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify({
        symbols: ['000001', '000002'],
        exchanges: ['SZ', 'SZ']
    })
})
.then(response => response.json())
.then(data => console.log(data));
```

## 注意事项

1. 当前版本使用模拟数据，实际部署时需要连接真实的数据源
2. 实时数据订阅功能需要在配置中启用
3. 建议在生产环境中配置适当的限流和缓存策略
4. 所有时间戳均为毫秒级Unix时间戳
5. 股票代码和交易所代码会自动转换为大写

## 交互式文档

启动服务后，可以访问以下地址查看交互式API文档：

- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc
