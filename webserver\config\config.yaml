# 金融市场数据服务配置文件

# 开发环境配置
development:
  debug: true
  host: "127.0.0.1"
  port: 8000
  log_level: "DEBUG"
  
  # 数据源配置
  data_source:
    type: "mock"
    update_interval: 0.1
  
  # 缓存配置
  cache:
    ttl: 60
    max_size: 1000

# 生产环境配置
production:
  debug: false
  host: "0.0.0.0"
  port: 8000
  log_level: "INFO"
  
  # 数据源配置
  data_source:
    type: "real"
    update_interval: 0.05
  
  # 缓存配置
  cache:
    ttl: 300
    max_size: 5000
  
  # 安全配置
  cors:
    allowed_origins:
      - "https://yourdomain.com"
      - "https://api.yourdomain.com"

# 测试环境配置
testing:
  debug: true
  host: "127.0.0.1"
  port: 8001
  log_level: "DEBUG"
  
  # 数据源配置
  data_source:
    type: "mock"
    update_interval: 1.0
  
  # 缓存配置
  cache:
    ttl: 30
    max_size: 100
