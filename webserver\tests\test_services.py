"""
服务层测试
"""

import pytest
from datetime import datetime

from app.services.market_data import MarketDataService
from app.services.subscription import SubscriptionService


class TestMarketDataService:
    """市场数据服务测试"""
    
    @pytest.fixture
    def market_service(self):
        """创建市场数据服务实例"""
        return MarketDataService()
    
    @pytest.mark.asyncio
    async def test_get_single_tick_data(self, market_service):
        """测试获取单个股票成交信息"""
        tick_data = await market_service.get_single_tick_data("000001", "SZ")
        
        assert tick_data is not None
        assert tick_data.symbol == "000001"
        assert tick_data.exchange == "SZ"
        assert tick_data.last_price > 0
        assert tick_data.volume >= 0
        assert isinstance(tick_data.datetime, datetime)
    
    @pytest.mark.asyncio
    async def test_get_multiple_tick_data_with_symbols(self, market_service):
        """测试获取多个指定股票的成交信息"""
        symbols = ["000001", "000002"]
        exchanges = ["SZ", "SZ"]
        
        tick_data_list = await market_service.get_multiple_tick_data(symbols, exchanges)
        
        assert isinstance(tick_data_list, list)
        assert len(tick_data_list) <= len(symbols)
        
        for tick_data in tick_data_list:
            assert tick_data.symbol in symbols
            assert tick_data.exchange in exchanges
    
    @pytest.mark.asyncio
    async def test_get_multiple_tick_data_all(self, market_service):
        """测试获取所有股票的成交信息"""
        tick_data_list = await market_service.get_multiple_tick_data([], [])
        
        assert isinstance(tick_data_list, list)
        assert len(tick_data_list) > 0
        
        # 验证返回的是模拟股票数据
        symbols = [tick.symbol for tick in tick_data_list]
        assert "000001" in symbols  # 应该包含模拟数据中的股票
    
    @pytest.mark.asyncio
    async def test_get_single_kline_data(self, market_service):
        """测试获取单个股票K线信息"""
        kline_data = await market_service.get_single_kline_data(
            "000001", "SZ", "1m", None, None, 10
        )
        
        assert isinstance(kline_data, list)
        assert len(kline_data) <= 10
        
        if kline_data:
            kline = kline_data[0]
            assert kline.symbol == "000001"
            assert kline.exchange == "SZ"
            assert kline.interval == "1m"
            assert kline.open > 0
            assert kline.close > 0
    
    @pytest.mark.asyncio
    async def test_get_multiple_kline_data(self, market_service):
        """测试获取多个股票K线信息"""
        symbols = ["000001", "000002"]
        exchanges = ["SZ", "SZ"]
        
        kline_data_list = await market_service.get_multiple_kline_data(
            symbols, exchanges, "1m", None, None, 5
        )
        
        assert isinstance(kline_data_list, list)
        assert len(kline_data_list) <= len(symbols)
        
        for kline_data in kline_data_list:
            assert "symbol" in kline_data
            assert "exchange" in kline_data
            assert "klines" in kline_data
            assert isinstance(kline_data["klines"], list)
    
    def test_generate_mock_tick_data(self, market_service):
        """测试生成模拟tick数据"""
        tick_data = market_service._generate_mock_tick_data("000001", "SZ")
        
        assert tick_data.symbol == "000001"
        assert tick_data.exchange == "SZ"
        assert tick_data.last_price > 0
        assert tick_data.pre_close > 0
        assert tick_data.volume >= 0
        assert tick_data.amount >= 0
        
        # 验证买卖盘数据
        assert tick_data.bid_price_1 > 0
        assert tick_data.ask_price_1 > 0
        assert tick_data.bid_volume_1 >= 0
        assert tick_data.ask_volume_1 >= 0
    
    def test_generate_mock_kline_data(self, market_service):
        """测试生成模拟K线数据"""
        kline_data = market_service._generate_mock_kline_data(
            "000001", "SZ", "1m", None, None, 5
        )
        
        assert isinstance(kline_data, list)
        assert len(kline_data) <= 5
        
        if kline_data:
            kline = kline_data[0]
            assert kline.symbol == "000001"
            assert kline.exchange == "SZ"
            assert kline.interval == "1m"
            assert kline.open > 0
            assert kline.high >= kline.open
            assert kline.low <= kline.open
    
    def test_get_interval_minutes(self, market_service):
        """测试时间间隔转换"""
        assert market_service._get_interval_minutes("1m") == 1
        assert market_service._get_interval_minutes("5m") == 5
        assert market_service._get_interval_minutes("1h") == 60
        assert market_service._get_interval_minutes("1d") == 1440
        assert market_service._get_interval_minutes("invalid") == 1
    
    def test_get_stock_name(self, market_service):
        """测试获取股票名称"""
        name = market_service._get_stock_name("000001", "SZ")
        assert name == "平安银行"
        
        # 测试未知股票
        name = market_service._get_stock_name("999999", "SZ")
        assert name == "股票999999"


class TestSubscriptionService:
    """订阅服务测试"""
    
    @pytest.fixture
    def subscription_service(self):
        """创建订阅服务实例"""
        return SubscriptionService()
    
    @pytest.mark.asyncio
    async def test_start_and_stop_service(self, subscription_service):
        """测试启动和停止订阅服务"""
        # 测试启动
        await subscription_service.start()
        assert subscription_service.is_running
        
        # 测试停止
        await subscription_service.stop()
        assert not subscription_service.is_running
    
    def test_subscribe_and_unsubscribe_symbol(self, subscription_service):
        """测试订阅和取消订阅股票"""
        symbol = "000001"
        exchange = "SZ"
        
        # 测试订阅
        subscription_service.subscribe_symbol(symbol, exchange)
        assert subscription_service.is_subscribed(symbol, exchange)
        assert f"{symbol}.{exchange}" in subscription_service.get_subscribed_symbols()
        
        # 测试取消订阅
        subscription_service.unsubscribe_symbol(symbol, exchange)
        assert not subscription_service.is_subscribed(symbol, exchange)
        assert f"{symbol}.{exchange}" not in subscription_service.get_subscribed_symbols()
    
    def test_get_full_tick_data_not_subscribed(self, subscription_service):
        """测试获取未订阅股票的实时数据"""
        tick_data = subscription_service.get_full_tick_data("000001", "SZ")
        assert tick_data is None
    
    def test_add_default_subscriptions(self, subscription_service):
        """测试添加默认订阅"""
        subscription_service.add_default_subscriptions()
        
        subscribed_symbols = subscription_service.get_subscribed_symbols()
        assert len(subscribed_symbols) > 0
        assert "000001.SZ" in subscribed_symbols  # 平安银行应该在默认订阅中
    
    def test_data_changed(self, subscription_service):
        """测试数据变化检测"""
        from app.models.response import TickDataResponse
        from datetime import datetime
        
        # 创建两个不同的tick数据
        tick1 = TickDataResponse(
            symbol="000001",
            exchange="SZ",
            datetime=datetime.now(),
            timestamp=1000,
            last_price=10.0,
            volume=1000
        )
        
        tick2 = TickDataResponse(
            symbol="000001",
            exchange="SZ",
            datetime=datetime.now(),
            timestamp=1001,
            last_price=10.1,
            volume=1000
        )
        
        # 测试数据变化检测
        assert subscription_service._data_changed(None, tick1)  # 新数据
        assert subscription_service._data_changed(tick1, tick2)  # 价格变化
        assert not subscription_service._data_changed(tick1, tick1)  # 相同数据
