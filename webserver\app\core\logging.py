"""
日志配置模块
"""

import os
import sys
from pathlib import Path

from loguru import logger

from config.settings import get_settings


def setup_logging():
    """设置日志配置"""
    settings = get_settings()
    log_config = settings.get_log_config()
    
    # 移除默认的日志处理器
    logger.remove()
    
    # 控制台日志处理器
    console_format = (
        "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
        "<level>{level: <8}</level> | "
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
        "<level>{message}</level>"
    )
    
    logger.add(
        sys.stdout,
        format=console_format,
        level=log_config["level"],
        colorize=True,
        backtrace=True,
        diagnose=True,
    )
    
    # 文件日志处理器
    log_file_path = Path(log_config["file"])
    
    # 确保日志目录存在
    log_file_path.parent.mkdir(parents=True, exist_ok=True)
    
    file_format = (
        "{time:YYYY-MM-DD HH:mm:ss.SSS} | "
        "{level: <8} | "
        "{name}:{function}:{line} | "
        "{message}"
    )
    
    logger.add(
        log_file_path,
        format=file_format,
        level=log_config["level"],
        rotation=log_config["rotation"],
        retention=log_config["retention"],
        compression="zip",
        backtrace=True,
        diagnose=True,
        encoding="utf-8",
    )
    
    # 错误日志单独文件
    error_log_path = log_file_path.parent / "error.log"
    logger.add(
        error_log_path,
        format=file_format,
        level="ERROR",
        rotation=log_config["rotation"],
        retention=log_config["retention"],
        compression="zip",
        backtrace=True,
        diagnose=True,
        encoding="utf-8",
    )
    
    # API访问日志
    api_log_path = log_file_path.parent / "api.log"
    logger.add(
        api_log_path,
        format=file_format,
        level="INFO",
        rotation=log_config["rotation"],
        retention=log_config["retention"],
        compression="zip",
        filter=lambda record: "api" in record["extra"],
        encoding="utf-8",
    )
    
    logger.info(f"日志系统初始化完成，日志级别: {log_config['level']}")
    logger.info(f"日志文件路径: {log_file_path}")


def get_api_logger():
    """获取API专用日志记录器"""
    return logger.bind(api=True)


class LoggerMixin:
    """日志记录器混入类"""
    
    @property
    def logger(self):
        """获取类专用的日志记录器"""
        return logger.bind(class_name=self.__class__.__name__)


def log_api_request(request_info: dict):
    """记录API请求日志"""
    api_logger = get_api_logger()
    api_logger.info(
        f"API请求: {request_info.get('method', 'UNKNOWN')} "
        f"{request_info.get('url', 'UNKNOWN')} "
        f"- 客户端: {request_info.get('client', 'UNKNOWN')}"
    )


def log_api_response(response_info: dict):
    """记录API响应日志"""
    api_logger = get_api_logger()
    api_logger.info(
        f"API响应: {response_info.get('status_code', 'UNKNOWN')} "
        f"- 耗时: {response_info.get('duration', 'UNKNOWN')}ms "
        f"- 大小: {response_info.get('size', 'UNKNOWN')}bytes"
    )
