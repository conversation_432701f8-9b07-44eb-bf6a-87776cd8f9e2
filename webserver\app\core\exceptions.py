"""
统一异常处理机制
"""

import traceback
from typing import Union

from fastapi import FastAPI, Request, status
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from loguru import logger
from pydantic import ValidationError

from app.models.response import ApiResponse


class MarketDataException(Exception):
    """市场数据相关异常基类"""
    
    def __init__(self, message: str, code: int = 500):
        self.message = message
        self.code = code
        super().__init__(self.message)


class DataNotFoundError(MarketDataException):
    """数据未找到异常"""
    
    def __init__(self, message: str = "数据未找到"):
        super().__init__(message, 404)


class DataSourceError(MarketDataException):
    """数据源异常"""
    
    def __init__(self, message: str = "数据源错误"):
        super().__init__(message, 503)


class ValidationError(MarketDataException):
    """参数验证异常"""
    
    def __init__(self, message: str = "参数验证失败"):
        super().__init__(message, 400)


class ServiceUnavailableError(MarketDataException):
    """服务不可用异常"""
    
    def __init__(self, message: str = "服务不可用"):
        super().__init__(message, 503)


def setup_exception_handlers(app: FastAPI):
    """设置异常处理器"""
    
    @app.exception_handler(MarketDataException)
    async def market_data_exception_handler(request: Request, exc: MarketDataException):
        """处理市场数据相关异常"""
        logger.error(f"市场数据异常: {exc.message}")
        
        return JSONResponse(
            status_code=exc.code,
            content=ApiResponse.error(
                code=exc.code,
                message=exc.message
            ).dict()
        )
    
    @app.exception_handler(RequestValidationError)
    async def validation_exception_handler(request: Request, exc: RequestValidationError):
        """处理请求参数验证异常"""
        error_details = []
        for error in exc.errors():
            field = " -> ".join(str(loc) for loc in error["loc"])
            message = error["msg"]
            error_details.append(f"{field}: {message}")
        
        error_message = "参数验证失败: " + "; ".join(error_details)
        logger.warning(f"请求参数验证失败: {error_message}")
        
        return JSONResponse(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            content=ApiResponse.error(
                code=422,
                message=error_message,
                data={"details": exc.errors()}
            ).model_dump()
        )
    
    @app.exception_handler(ValidationError)
    async def pydantic_validation_exception_handler(request: Request, exc: ValidationError):
        """处理Pydantic验证异常"""
        error_details = []
        for error in exc.errors():
            field = " -> ".join(str(loc) for loc in error["loc"])
            message = error["msg"]
            error_details.append(f"{field}: {message}")
        
        error_message = "数据验证失败: " + "; ".join(error_details)
        logger.warning(f"数据验证失败: {error_message}")
        
        return JSONResponse(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            content=ApiResponse.error(
                code=422,
                message=error_message,
                data={"details": exc.errors()}
            ).model_dump()
        )
    
    @app.exception_handler(ValueError)
    async def value_error_handler(request: Request, exc: ValueError):
        """处理值错误异常"""
        error_message = f"参数错误: {str(exc)}"
        logger.warning(error_message)
        
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content=ApiResponse.error(
                code=400,
                message=error_message
            ).model_dump()
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception):
        """处理通用异常"""
        error_message = "服务器内部错误"
        
        # 记录详细错误信息
        logger.error(f"未处理的异常: {type(exc).__name__}: {str(exc)}")
        logger.error(f"异常堆栈: {traceback.format_exc()}")
        
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=ApiResponse.error(
                code=500,
                message=error_message
            ).model_dump()
        )
    
    logger.info("异常处理器设置完成")
