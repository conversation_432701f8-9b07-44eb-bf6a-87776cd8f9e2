-- 取Client配置

function()
    local ret = {
        app = {
            appName = "XtQuantService",
            netThreadNum = 5,
            netProcessThreadNum = 1,
            dispatcherThreadNum = 5,
            reportSeconds = 60,
            logPath = "../config/xtquantservice.log4cxx",
            logWatch = 1,
            appendDate = 1,
            timeoutSec = 0,
            requestTimeoutSec = 150,
        },
        threadPools = {
            --datasource
            datasource_callback = 10,
            datasource_process = 3,
            datasource_req = 10,
            datasource_other = 5,
            datasource_tradedate_change = 1,
            whole_quote = 2,
            -- trade linkage
            linkage = 1,
            accounts = 5,
            persistmini = 1,
            subquote = 3,
            --python策略运行
            pystrategy_run = 3,
            --msgservice
            msg_service = 1,
            uicontrol_hpf_data_model = 1,
            server_miniquote = 1,
            server_vbaservice = 1,
            --指标全推
            index_quote = 2,
        },
        client_xtservice = {
            tagTemplate = "xtservice",
            address = g_xtservice_address,
            isGetdAddressFromNameServer = 0,
            reconnectSecond = -1,
            timeoutSecond = 120,
            requestTimeoutSecond = 150,
            isUseSSL = 0,
            sslCaPath = "../data/server.crt",
        },
        client_xtmarketinfo = {
            tagTemplate = "xtmarketinfo",
            isGetdAddressFromNameServer=0,
            proxyType=0,
            requestTimeoutSecond=150,
            proxyNeedCheck=0,
            isUsedAloneIO=0,
            isUseSSL=0,
            address = g_defaultPorts["xtmarketinfo"],
            keepAliveCheckSecond=5,
            proxyPort=80,
            reconnectSecond = 3,
            timeoutSecond=120,
        },
        server_xtquant = {
            tag = "server_miniquote",
            address = "0.0.0.0:58610",
            isGetdAddressFromNameServer=0,
            timeoutSecond=0,
            keepAliveCheckSecond=0,
            maxConnectionNum = 10,
            isAutoBind = 0,
            isUseSSL=0,
        },
        metaInfo = {
            ["2"] = "86400000",
            ["1008"] = "0",
            ["1009"] = "0",
            ["1010"] = "0",
            ["1011"] = "0",
            ["1801"] = "0",
            ["1802"] = "0",
            ["1803"] = "60000,86400000",
            ["1804"] = "0",
            ["1806"] = "0",
            ["1808"] = "60000,86400000",
            ["1820"] = "0",
            ["1830"] = "0",
            ["2000"] = "86400000",
            ["2001"] = "86400000",
            ["2002"] = "86400000",
            ["2002"] = "86400000",
            ["2003"] = "86400000",
            ["2004"] = "86400000",
            ["2006"] = "86400000",
            ["2013"] = "86400000",
            ["2014"] = "0",
            ["2016"] = "86400000",
            ["2017"] = "60000",
            ["2018"] = "60000",
            ["2999"] = "86400000",
            ["3000"] = "0",
            ["3001"] = "60000,300000,3600000,86400000",
            ["3002"] = "60000",
            ["3004"] = "60000",
            ["3006"] = "60000,86400000",
            ["3013"] = "86400000",
            ["3014"] = "60000,86400000",
            ["3015"] = "86400000",
            ["3017"] = "0,86400000",
            ["3018"] = "86400000",
            ["3019"] = "86400000",
            ["3030"] = "0",
            ["4000"] = "86400000",
            ["4002"] = "60000,300000,3600000,86400000",
            ["4003"] = "60000,300000,3600000,86400000",
            ["4011"] = "60000,300000,3600000,86400000",
            ["4999"] = "86400000",
            ["5000"] = "0",
            ["5002"] = "86400000",
            ["5003"] = "0",
            ["9000"] = "0,86400000", 
        },
        config = {
            configdir = "../config",
            datadir = "../userdata/xtquant",
            modeldir = "../config/user",
        }
    }   
    return ret
end
