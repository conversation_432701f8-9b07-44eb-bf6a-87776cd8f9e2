# 金融市场数据服务

基于 FastAPI 的金融市场数据服务，提供股票成交信息、K线数据等金融市场数据的API接口。

## 功能特性

- 🚀 **高性能**: 基于 FastAPI 和异步编程
- 📊 **实时数据**: 支持实时tick数据订阅和推送
- 🔄 **批量查询**: 支持单个和批量股票数据查询
- 📈 **K线数据**: 支持多种时间周期的K线数据
- 🛡️ **异常处理**: 完善的错误处理和日志记录
- 📝 **API文档**: 自动生成的交互式API文档
- 🧪 **测试覆盖**: 完整的单元测试和集成测试
- ⚙️ **配置管理**: 支持多环境配置

## 技术栈

- **Web框架**: FastAPI 0.104.1
- **异步运行时**: uvicorn
- **数据验证**: Pydantic 2.5.0
- **日志记录**: loguru
- **测试框架**: pytest
- **配置管理**: pydantic-settings + YAML

## 快速开始

### 环境要求

- Python 3.8+
- pip

### 安装和运行

#### Windows

```bash
# 克隆项目（如果适用）
git clone <repository-url>
cd webserver

# 运行启动脚本
start.bat
```

#### Linux/macOS

```bash
# 克隆项目（如果适用）
git clone <repository-url>
cd webserver

# 给启动脚本执行权限
chmod +x start.sh

# 运行启动脚本
./start.sh
```

#### 手动安装

```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows:
venv\Scripts\activate
# Linux/macOS:
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 启动服务
python scripts/start.py --env development --reload
```

### 访问服务

启动成功后，可以访问：

- **API服务**: http://localhost:8000
- **API文档**: http://localhost:8000/docs
- **ReDoc文档**: http://localhost:8000/redoc

## 配置说明

### 环境变量

创建 `.env` 文件（参考 `.env.example`）：

```env
DEBUG=true
HOST=127.0.0.1
PORT=8000
LOG_LEVEL=INFO
TICK_SUBSCRIPTION_ENABLED=true
```

### 配置文件

编辑 `config/config.yaml` 文件：

```yaml
development:
  debug: true
  host: "127.0.0.1"
  port: 8000
  log_level: "DEBUG"
```

## API 使用示例

### 获取单个股票成交信息

```bash
curl "http://localhost:8000/api/v1/market/tick/000001?exchange=SZ"
```

### 获取K线数据

```bash
curl "http://localhost:8000/api/v1/market/kline/000001?exchange=SZ&interval=1m&limit=10"
```

### 批量获取股票信息

```bash
curl -X POST "http://localhost:8000/api/v1/market/tick/multiple" \
     -H "Content-Type: application/json" \
     -d '{"symbols": ["000001", "000002"], "exchanges": ["SZ", "SZ"]}'
```

## 开发指南

### 项目结构

```
webserver/
├── main.py                 # 应用入口
├── requirements.txt        # 依赖包
├── config/                 # 配置管理
│   ├── settings.py
│   └── config.yaml
├── app/                    # 应用代码
│   ├── models/            # 数据模型
│   ├── services/          # 业务逻辑
│   ├── api/               # API路由
│   ├── core/              # 核心功能
│   └── utils/             # 工具函数
├── tests/                 # 测试代码
├── scripts/               # 启动脚本
└── docs/                  # 文档
```

### 运行测试

```bash
# 运行所有测试
pytest

# 运行特定测试文件
pytest tests/test_api.py

# 运行测试并生成覆盖率报告
pytest --cov=app --cov-report=html
```

### 代码格式化

```bash
# 安装开发依赖
pip install black isort flake8

# 格式化代码
black .
isort .

# 检查代码风格
flake8 .
```

## 部署指南

### 生产环境部署

1. **设置环境变量**:
   ```bash
   export APP_ENV=production
   export DEBUG=false
   export HOST=0.0.0.0
   export PORT=8000
   ```

2. **启动服务**:
   ```bash
   python scripts/start.py --env production --workers 4
   ```

3. **使用进程管理器**（推荐）:
   ```bash
   # 使用 gunicorn
   gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
   
   # 或使用 supervisor、systemd 等
   ```

### Docker 部署

创建 `Dockerfile`:

```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8000

CMD ["python", "scripts/start.py", "--env", "production", "--host", "0.0.0.0"]
```

## 监控和日志

### 日志文件

- **应用日志**: `logs/app.log`
- **错误日志**: `logs/error.log`
- **API访问日志**: `logs/api.log`

### 健康检查

```bash
# 基础健康检查
curl http://localhost:8000/health

# 订阅服务状态
curl http://localhost:8000/api/v1/market/subscription/status
```

## 常见问题

### Q: 如何连接真实的数据源？

A: 当前版本使用模拟数据。要连接真实数据源，需要：
1. 修改 `app/services/market_data.py` 中的数据获取逻辑
2. 实现具体的数据源连接器
3. 配置相应的认证和连接参数

### Q: 如何扩展API功能？

A: 可以通过以下步骤扩展：
1. 在 `app/models/` 中定义新的数据模型
2. 在 `app/services/` 中实现业务逻辑
3. 在 `app/api/v1/` 中添加新的路由
4. 编写相应的测试用例

### Q: 如何优化性能？

A: 性能优化建议：
1. 启用数据缓存
2. 使用连接池
3. 配置适当的工作进程数
4. 实现数据分页
5. 添加限流机制

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证。

## 联系方式

如有问题或建议，请联系开发团队。
